package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 行驶时间数据访问接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface TravelTimeMapper extends BaseMapper<TravelTime> {

    /**
     * 获取总记录数
     */
    @Select("SELECT COUNT(*) FROM travel_time")
    Long getTotalCount();

    /**
     * 根据偏移量获取样本数据
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 样本数据列表
     */
    @Select("SELECT longitude_start, latitude_start, longitude_end, latitude_end, travel_time " +
            "FROM travel_time " +
            "WHERE longitude_start != '0' AND latitude_start != '0' " +
            "AND longitude_end != '0' AND latitude_end != '0' " +
            "AND travel_time > 0 " +
            "LIMIT #{offset}, #{limit}")
    List<TravelTime> getSampleByOffset(int offset, int limit);
}
