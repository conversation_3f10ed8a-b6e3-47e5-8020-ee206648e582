2025-08-18 01:29:20.402 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.ict.ycwl.pathcalculate.TravelTimeValidationTest], using SpringBootContextLoader
2025-08-18 01:29:20.407 [main] INFO  o.s.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.ict.ycwl.pathcalculate.TravelTimeValidationTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-08-18 01:29:20.408 [main] INFO  o.s.t.c.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.ict.ycwl.pathcalculate.TravelTimeValidationTest]: TravelTimeValidationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-08-18 01:29:20.536 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.ict.ycwl.pathcalculate.PathCalculateApplication for test class com.ict.ycwl.pathcalculate.TravelTimeValidationTest
2025-08-18 01:29:20.650 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-08-18 01:29:20.669 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@409c54f, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@3e74829, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@4f6f416f, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3b8f0a79, org.springframework.test.context.support.DirtiesContextTestExecutionListener@71e693fa, org.springframework.test.context.transaction.TransactionalTestExecutionListener@48793bef, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@7d286fb6, org.springframework.test.context.event.EventPublishingTestExecutionListener@3eb77ea8, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@7b8b43c7, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@7aaca91a, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@44c73c26, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@41005828, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@60b4beb4, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@7fcf2fc1]
2025-08-18 01:29:23.211 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-08-18 01:29:32.127 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[pathcalculate-test] & group[DEFAULT_GROUP]
2025-08-18 01:29:35.165 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[pathcalculate-test.yaml] & group[DEFAULT_GROUP]
2025-08-18 01:29:38.199 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[pathcalculate-test-test.yaml] & group[DEFAULT_GROUP]
2025-08-18 01:29:38.260 [main] INFO  c.ict.ycwl.pathcalculate.TravelTimeValidationTest - The following profiles are active: test
2025-08-18 01:29:41.918 [main] WARN  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [0] datasource,Please add your primary datasource or check your configuration
2025-08-18 01:29:43.599 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "groupId" is the table primary key by @TableId annotation in Class: "com.ict.ycwl.pathcalculate.pojo.Group",So @TableField annotation will not work!
2025-08-18 01:29:43.733 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.ict.ycwl.pathcalculate.pojo.SystemParameter"
2025-08-18 01:29:43.766 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "versionId" is primitive !不建议如此请使用包装类 in Class: "com.ict.ycwl.pathcalculate.pojo.Version"
2025-08-18 01:29:43.775 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.ict.ycwl.pathcalculate.mapper.VersionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-18 01:29:43.777 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.ict.ycwl.pathcalculate.mapper.VersionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-18 01:29:43.778 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.ict.ycwl.pathcalculate.mapper.VersionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-18 01:29:44.410 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'newAlgorithmController': Unsatisfied dependency expressed through field 'databaseToAlgorithmAdapter'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'databaseToAlgorithmAdapter': Unsatisfied dependency expressed through field 'travelTimeMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'travelTimeMapper' defined in file [D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes\com\ict\ycwl\pathcalculate\mapper\TravelTimeMapper.class]: Invocation of init method failed; nested exception is java.lang.NoSuchMethodError: org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;
2025-08-18 01:29:44.411 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-18 01:29:44.411 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-18 01:29:44.418 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder$AnnotationWrapper.<init>(MybatisMapperAnnotationBuilder.java:685)

The following method did not exist:

    org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;

The method's class, org.apache.ibatis.annotations.Select, is available from the following locations:

    jar:file:/D:/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar!/org/apache/ibatis/annotations/Select.class

The class hierarchy was loaded from the following locations:

    org.apache.ibatis.annotations.Select: file:/D:/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar


Action:

Correct the classpath of your application so that it contains a single, compatible version of org.apache.ibatis.annotations.Select

2025-08-18 01:29:44.419 [main] ERROR o.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@409c54f] to prepare test instance [com.ict.ycwl.pathcalculate.TravelTimeValidationTest@4fd7b79]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1380)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'newAlgorithmController': Unsatisfied dependency expressed through field 'databaseToAlgorithmAdapter'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'databaseToAlgorithmAdapter': Unsatisfied dependency expressed through field 'travelTimeMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'travelTimeMapper' defined in file [D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes\com\ict\ycwl\pathcalculate\mapper\TravelTimeMapper.class]: Invocation of init method failed; nested exception is java.lang.NoSuchMethodError: org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 65 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'databaseToAlgorithmAdapter': Unsatisfied dependency expressed through field 'travelTimeMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'travelTimeMapper' defined in file [D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes\com\ict\ycwl\pathcalculate\mapper\TravelTimeMapper.class]: Invocation of init method failed; nested exception is java.lang.NoSuchMethodError: org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 85 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'travelTimeMapper' defined in file [D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes\com\ict\ycwl\pathcalculate\mapper\TravelTimeMapper.class]: Invocation of init method failed; nested exception is java.lang.NoSuchMethodError: org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1799)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 99 common frames omitted
Caused by: java.lang.NoSuchMethodError: org.apache.ibatis.annotations.Select.databaseId()Ljava/lang/String;
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder$AnnotationWrapper.<init>(MybatisMapperAnnotationBuilder.java:685)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.lambda$getAnnotationWrapper$4(MybatisMapperAnnotationBuilder.java:653)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:270)
	at java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.getAnnotationWrapper(MybatisMapperAnnotationBuilder.java:654)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.getAnnotationWrapper(MybatisMapperAnnotationBuilder.java:646)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:105)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:95)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:126)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	... 109 common frames omitted
2025-08-18 01:29:44.435 [Thread-2] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-18 01:29:44.436 [Thread-2] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-18 01:34:18.238 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🧪 开始Travel Time数据准确性验证测试（简化版）
2025-08-18 01:34:18.240 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 配置信息: 抽样50条, 请求间隔300ms, 允许误差20.0%
2025-08-18 01:34:18.241 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🎯 开始从数据库抽取样本数据...
2025-08-18 01:34:20.194 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 数据库有效记录数: 2873030
2025-08-18 01:34:52.292 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 成功抽取50条有效样本
2025-08-18 01:34:52.292 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 成功抽取50条样本数据
2025-08-18 01:34:52.292 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第1/50条: (113.62832,24.829475) -> (113.621501,24.699899)
2025-08-18 01:34:53.573 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:53.887 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第2/50条: (113.934611,24.978389) -> (113.346737,25.177347)
2025-08-18 01:34:53.968 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.41%)
2025-08-18 01:34:54.273 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第3/50条: (113.529925,24.54474) -> (113.322701,24.739501)
2025-08-18 01:34:54.361 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:54.677 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第4/50条: (113.461557,25.169422) -> (113.551357,24.835894)
2025-08-18 01:34:54.757 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:55.061 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第5/50条: (113.504393,24.91505) -> (114.315055,25.121929)
2025-08-18 01:34:55.155 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:55.465 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第6/50条: (113.598257,24.526049) -> (114.238457,24.071212)
2025-08-18 01:34:55.605 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过110.57%)
2025-08-18 01:34:55.915 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第7/50条: (114.303569,25.123947) -> (113.068204,25.188629)
2025-08-18 01:34:56.249 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 01:34:56.556 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第8/50条: (114.514696,25.304417) -> (113.458157,24.728681)
2025-08-18 01:34:56.640 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过110.11%)
2025-08-18 01:34:56.947 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第9/50条: (113.061544,25.282525) -> (113.04757,25.28601)
2025-08-18 01:34:57.003 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.6666579571761773%)
2025-08-18 01:34:57.308 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第10/50条: (113.345696,25.122337) -> (113.231143,24.6236)
2025-08-18 01:34:57.405 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.4%)
2025-08-18 01:34:57.710 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第11/50条: (113.799477,25.160434) -> (113.593972,24.781168)
2025-08-18 01:34:57.778 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:58.087 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第12/50条: (113.819424,24.788007) -> (113.733613,25.089755)
2025-08-18 01:34:58.175 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:58.490 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第13/50条: (113.35119,25.139521) -> (113.293668,24.772292)
2025-08-18 01:34:58.592 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:58.895 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第14/50条: (113.676561,24.61051) -> (113.657766,24.602056)
2025-08-18 01:34:58.940 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:34:59.241 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第15/50条: (113.665176,24.77502) -> (113.600182,24.67531)
2025-08-18 01:34:59.313 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过19.415967901090404%)
2025-08-18 01:34:59.616 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第16/50条: (113.41161,25.13777) -> (113.58508,24.798956)
2025-08-18 01:34:59.723 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:00.036 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第17/50条: (113.458157,24.728681) -> (113.523934,24.751943)
2025-08-18 01:35:00.091 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:00.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第18/50条: (114.028633,24.945119) -> (114.520165,25.077396)
2025-08-18 01:35:00.463 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.33553858108895%)
2025-08-18 01:35:00.763 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第19/50条: (113.285764,24.780136) -> (113.602551,24.675288)
2025-08-18 01:35:00.843 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.05%)
2025-08-18 01:35:01.152 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第20/50条: (113.605291,24.841961) -> (113.561667,24.780641)
2025-08-18 01:35:02.236 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.91%)
2025-08-18 01:35:02.539 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第21/50条: (114.121644,24.899772) -> (113.607056,24.804331)
2025-08-18 01:35:02.628 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.78%)
2025-08-18 01:35:02.932 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第22/50条: (113.53318,24.89166) -> (114.142994,24.331111)
2025-08-18 01:35:03.043 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.3%)
2025-08-18 01:35:03.355 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第23/50条: (113.697143,24.76541) -> (113.043141,25.084925)
2025-08-18 01:35:03.445 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过98.78%)
2025-08-18 01:35:03.756 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第24/50条: (113.572042,24.822559) -> (113.667607,24.614128)
2025-08-18 01:35:03.843 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.24%)
2025-08-18 01:35:04.146 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第25/50条: (113.387391,24.831489) -> (113.757643,25.095525)
2025-08-18 01:35:04.455 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:04.767 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第26/50条: (113.273147,24.775665) -> (113.586502,24.804095)
2025-08-18 01:35:04.828 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.23%)
2025-08-18 01:35:05.143 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第27/50条: (113.358771,25.109612) -> (114.131076,24.359713)
2025-08-18 01:35:05.270 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 01:35:05.579 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第28/50条: (114.026327,25.185653) -> (114.215885,24.85931)
2025-08-18 01:35:05.672 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过87.32%)
2025-08-18 01:35:05.984 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第29/50条: (113.526114,24.778843) -> (113.060297,25.283954)
2025-08-18 01:35:06.072 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.12%)
2025-08-18 01:35:06.385 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第30/50条: (113.748602,25.087874) -> (113.681038,24.773209)
2025-08-18 01:35:06.468 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:06.775 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第31/50条: (113.609684,24.697007) -> (113.585104,24.804815)
2025-08-18 01:35:06.846 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.18%)
2025-08-18 01:35:07.148 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第32/50条: (113.339715,25.127185) -> (114.100904,24.952666)
2025-08-18 01:35:07.246 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.89%)
2025-08-18 01:35:07.553 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第33/50条: (113.605666,24.790311) -> (113.740337,25.086177)
2025-08-18 01:35:07.640 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:07.943 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第34/50条: (113.348224,25.120149) -> (113.432578,24.943395)
2025-08-18 01:35:08.012 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:08.318 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第35/50条: (113.558708,24.799452) -> (113.52577,24.785277)
2025-08-18 01:35:08.375 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:08.690 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第36/50条: (113.552856,24.800859) -> (113.58453,24.765113)
2025-08-18 01:35:08.748 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过9.94%)
2025-08-18 01:35:09.062 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第37/50条: (113.577958,24.795753) -> (114.213167,24.06088)
2025-08-18 01:35:09.166 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 01:35:09.482 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第38/50条: (113.592613,24.770538) -> (113.621501,24.699899)
2025-08-18 01:35:09.550 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:09.859 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第39/50条: (113.74616,24.833962) -> (113.593094,24.776828)
2025-08-18 01:35:09.937 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.54%)
2025-08-18 01:35:10.249 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第40/50条: (114.371743,25.234405) -> (114.141064,24.916167)
2025-08-18 01:35:10.337 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.0%)
2025-08-18 01:35:10.651 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第41/50条: (114.496002,25.225061) -> (114.075796,24.953586)
2025-08-18 01:35:10.740 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.38921690915217%)
2025-08-18 01:35:11.043 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第42/50条: (113.569729,25.003112) -> (114.454758,25.259703)
2025-08-18 01:35:11.157 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:11.464 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第43/50条: (113.04757,25.28601) -> (113.284758,24.773964)
2025-08-18 01:35:11.533 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.73%)
2025-08-18 01:35:11.839 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第44/50条: (113.614493,24.675472) -> (113.588731,24.742704)
2025-08-18 01:35:11.906 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.07%)
2025-08-18 01:35:12.210 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第45/50条: (114.315609,25.113869) -> (113.586702,24.800764)
2025-08-18 01:35:12.294 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.54%)
2025-08-18 01:35:12.598 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第46/50条: (113.357829,25.122181) -> (113.586163,24.805247)
2025-08-18 01:35:13.049 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:13.358 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第47/50条: (113.4652,25.158874) -> (113.580103,24.79825)
2025-08-18 01:35:13.442 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:13.747 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第48/50条: (113.359923,25.119563) -> (113.466977,25.155513)
2025-08-18 01:35:14.808 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:15.109 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第49/50条: (113.604889,24.81119) -> (114.308327,25.107171)
2025-08-18 01:35:15.191 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.27%)
2025-08-18 01:35:15.499 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第50/50条: (113.293366,24.738772) -> (113.539143,24.799094)
2025-08-18 01:35:15.564 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 01:35:15.877 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 开始生成验证报告...
2025-08-18 01:35:15.884 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证报告已生成: target/test-results/simple_travel_time_validation_20250818_013515.txt
2025-08-18 01:35:15.885 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 验证结果摘要: 总数50, 通过21, 失败29, 错误0, 通过率{:.1f}%
2025-08-18 01:37:06.952 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🧪 开始Travel Time数据准确性验证测试（简化版）
2025-08-18 01:37:06.954 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 配置信息: 抽样100000条, 请求间隔300ms, 允许误差10.0%
2025-08-18 01:37:06.955 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🎯 开始从数据库抽取样本数据...
2025-08-18 01:37:08.532 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 数据库有效记录数: 2873030
2025-08-18 01:44:13.592 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🧪 开始Travel Time数据准确性验证测试（简化版）
2025-08-18 01:44:13.594 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 配置信息: 抽样1000条, 请求间隔300ms, 允许误差10.0%
2025-08-18 01:44:13.595 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🎯 开始从数据库抽取样本数据...
2025-08-18 01:44:15.037 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 数据库有效记录数: 2873030
2025-08-18 01:51:34.479 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🧪 开始Travel Time数据准确性验证测试（简化版）
2025-08-18 01:51:34.481 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 配置信息: 抽样1000条, 请求间隔300ms, 允许误差10.0%
2025-08-18 01:51:34.482 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🎯 开始从数据库抽取样本数据...
2025-08-18 01:51:36.155 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 📊 数据库有效记录数: 2873030
2025-08-18 02:01:44.823 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 成功抽取1000条有效样本
2025-08-18 02:01:44.824 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 成功抽取1000条样本数据
2025-08-18 02:01:44.824 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第1/1000条: (114.024063,25.18527) -> (113.586858,24.817181)
2025-08-18 02:01:46.259 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:01:46.572 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第2/1000条: (113.272826,24.772738) -> (114.12021,24.34655)
2025-08-18 02:01:46.673 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过96.32%)
2025-08-18 02:01:46.975 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第3/1000条: (113.580213,24.800478) -> (113.463317,24.879027)
2025-08-18 02:01:47.066 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.33%)
2025-08-18 02:01:47.377 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第4/1000条: (113.60129,24.801469) -> (113.352745,25.178968)
2025-08-18 02:01:47.468 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.26%)
2025-08-18 02:01:47.780 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第5/1000条: (113.58724,24.789703) -> (113.575924,24.797951)
2025-08-18 02:01:47.825 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.0622551168029055%)
2025-08-18 02:01:48.137 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第6/1000条: (113.364472,25.129208) -> (114.214861,24.059694)
2025-08-18 02:01:48.241 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:01:48.544 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第7/1000条: (114.306,25.116931) -> (114.146249,25.018674)
2025-08-18 02:01:48.603 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.96%)
2025-08-18 02:01:48.916 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第8/1000条: (113.341367,25.076486) -> (113.697143,24.76541)
2025-08-18 02:01:48.990 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.49%)
2025-08-18 02:01:49.293 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第9/1000条: (113.821488,24.684006) -> (113.541131,24.800106)
2025-08-18 02:01:49.361 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.81%)
2025-08-18 02:01:49.668 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第10/1000条: (114.293769,25.115989) -> (113.352789,25.126153)
2025-08-18 02:01:49.772 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过100.67%)
2025-08-18 02:01:50.087 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第11/1000条: (113.602105,24.791121) -> (113.606633,24.843078)
2025-08-18 02:01:50.153 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:01:50.461 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第12/1000条: (113.592182,24.809154) -> (113.347218,25.391851)
2025-08-18 02:01:50.561 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.74%)
2025-08-18 02:01:50.863 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第13/1000条: (114.307626,25.110196) -> (114.131076,24.359713)
2025-08-18 02:01:50.956 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.44%)
2025-08-18 02:01:51.266 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第14/1000条: (114.383163,25.181234) -> (114.018172,24.240451)
2025-08-18 02:01:51.370 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过100.45%)
2025-08-18 02:01:51.671 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第15/1000条: (113.641896,24.638732) -> (113.602081,24.682911)
2025-08-18 02:01:51.741 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:01:52.043 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第16/1000条: (113.623112,24.688701) -> (113.743802,25.083982)
2025-08-18 02:01:52.124 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.6%)
2025-08-18 02:01:52.434 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第17/1000条: (114.30571,25.116291) -> (113.667607,24.614128)
2025-08-18 02:01:52.534 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.41%)
2025-08-18 02:01:52.839 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第18/1000条: (114.49643,25.225511) -> (114.205697,24.057728)
2025-08-18 02:01:52.959 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:01:53.274 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第19/1000条: (113.142074,24.539945) -> (113.298075,24.776911)
2025-08-18 02:01:53.353 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.99%)
2025-08-18 02:01:53.665 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第20/1000条: (113.601732,24.693728) -> (113.627971,25.111138)
2025-08-18 02:01:53.758 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:01:54.069 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第21/1000条: (113.045956,25.283825) -> (113.60219,24.815619)
2025-08-18 02:01:54.171 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.69%)
2025-08-18 02:01:54.475 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第22/1000条: (113.595006,24.571516) -> (113.858383,24.267485)
2025-08-18 02:01:54.536 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.66%)
2025-08-18 02:01:54.851 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第23/1000条: (114.30571,25.116291) -> (114.590166,25.198902)
2025-08-18 02:01:54.930 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过45.93092187703785%)
2025-08-18 02:01:55.237 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第24/1000条: (113.587335,24.79831) -> (113.532832,24.751522)
2025-08-18 02:01:55.306 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.6%)
2025-08-18 02:01:55.610 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第25/1000条: (113.676561,24.61051) -> (114.317904,25.121835)
2025-08-18 02:01:55.705 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.18%)
2025-08-18 02:01:56.016 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第26/1000条: (113.720754,24.771364) -> (113.598416,24.80681)
2025-08-18 02:01:56.088 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过18.17%)
2025-08-18 02:01:56.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第27/1000条: (113.524565,24.787409) -> (113.571284,24.798596)
2025-08-18 02:01:56.451 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过9.679390477608068%)
2025-08-18 02:01:56.766 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第28/1000条: (113.590201,24.797902) -> (114.015622,24.189406)
2025-08-18 02:01:56.864 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.08%)
2025-08-18 02:01:57.168 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第29/1000条: (113.288158,25.356661) -> (114.221539,24.06301)
2025-08-18 02:01:57.302 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:01:57.607 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第30/1000条: (114.358895,25.191476) -> (113.666729,24.856092)
2025-08-18 02:01:57.679 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.18%)
2025-08-18 02:01:57.993 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第31/1000条: (114.364493,25.193861) -> (114.114755,24.361048)
2025-08-18 02:01:58.103 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.23%)
2025-08-18 02:01:58.412 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第32/1000条: (113.584454,24.782079) -> (113.310888,24.75691)
2025-08-18 02:01:58.494 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:01:58.802 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第33/1000条: (113.605666,24.790311) -> (114.356491,25.20927)
2025-08-18 02:01:58.897 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.42%)
2025-08-18 02:01:59.197 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第34/1000条: (113.586887,24.807675) -> (112.925816,25.276824)
2025-08-18 02:01:59.295 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过88.59%)
2025-08-18 02:01:59.607 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第35/1000条: (113.587449,24.794473) -> (114.08887,24.687084)
2025-08-18 02:01:59.698 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.27%)
2025-08-18 02:01:59.999 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第36/1000条: (114.350839,25.164156) -> (114.31184,25.318629)
2025-08-18 02:02:00.070 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过21.45%)
2025-08-18 02:02:00.374 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第37/1000条: (114.555614,25.161188) -> (113.279602,24.778606)
2025-08-18 02:02:00.490 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:00.790 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第38/1000条: (113.397883,25.345243) -> (113.757754,24.892904)
2025-08-18 02:02:00.891 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.10676999955469%)
2025-08-18 02:02:01.192 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第39/1000条: (113.561426,24.843879) -> (114.071974,24.9498)
2025-08-18 02:02:01.258 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.93%)
2025-08-18 02:02:01.564 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第40/1000条: (113.533729,24.753516) -> (113.446984,24.751947)
2025-08-18 02:02:01.613 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.40666140547154%)
2025-08-18 02:02:01.917 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第41/1000条: (113.697143,24.76541) -> (113.809305,24.786451)
2025-08-18 02:02:01.968 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:02.276 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第42/1000条: (114.313822,25.119112) -> (113.602522,24.522024)
2025-08-18 02:02:02.377 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.48%)
2025-08-18 02:02:02.683 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第43/1000条: (113.545994,24.738085) -> (114.02577,25.186529)
2025-08-18 02:02:02.778 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.20036168576291%)
2025-08-18 02:02:03.088 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第44/1000条: (114.383163,25.181234) -> (113.698505,24.767171)
2025-08-18 02:02:03.186 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.76%)
2025-08-18 02:02:03.491 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第45/1000条: (113.519957,24.767664) -> (113.293688,24.775343)
2025-08-18 02:02:03.562 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:03.864 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第46/1000条: (113.578603,24.805405) -> (113.52998,24.767209)
2025-08-18 02:02:03.928 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:04.239 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第47/1000条: (113.541735,24.799046) -> (113.569352,24.80258)
2025-08-18 02:02:04.286 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:04.601 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第48/1000条: (113.74616,24.833962) -> (114.60175,25.260704)
2025-08-18 02:02:04.676 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过87.84%)
2025-08-18 02:02:04.989 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第49/1000条: (113.754657,25.074007) -> (113.601225,24.677987)
2025-08-18 02:02:05.052 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.99380661448639%)
2025-08-18 02:02:05.359 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第50/1000条: (113.536054,24.752073) -> (113.581682,24.803563)
2025-08-18 02:02:05.429 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.152922278855796%)
2025-08-18 02:02:05.733 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第51/1000条: (113.348224,25.120149) -> (114.202214,24.053731)
2025-08-18 02:02:05.838 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:06.152 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第52/1000条: (114.426929,25.0811) -> (113.040821,25.286564)
2025-08-18 02:02:06.272 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:06.587 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第53/1000条: (113.587183,24.795651) -> (113.748602,25.087874)
2025-08-18 02:02:06.668 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过38.7%)
2025-08-18 02:02:06.976 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第54/1000条: (113.057208,25.213304) -> (113.599277,24.683307)
2025-08-18 02:02:07.085 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.01%)
2025-08-18 02:02:07.397 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第55/1000条: (113.591159,24.67104) -> (114.213354,24.057246)
2025-08-18 02:02:07.488 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过108.71%)
2025-08-18 02:02:07.802 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第56/1000条: (113.523381,24.779982) -> (113.54384,24.764802)
2025-08-18 02:02:07.858 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.049958096299428%)
2025-08-18 02:02:08.159 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第57/1000条: (113.057929,25.283982) -> (113.746723,25.039896)
2025-08-18 02:02:08.240 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.48%)
2025-08-18 02:02:08.550 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第58/1000条: (114.389406,25.16176) -> (114.295207,25.109818)
2025-08-18 02:02:08.620 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:08.924 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第59/1000条: (113.532905,24.545201) -> (113.391777,25.312724)
2025-08-18 02:02:09.001 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过92.57%)
2025-08-18 02:02:09.312 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第60/1000条: (113.589084,24.773266) -> (113.536028,24.897287)
2025-08-18 02:02:09.364 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:09.671 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第61/1000条: (113.57543,24.823916) -> (114.301585,25.110472)
2025-08-18 02:02:09.747 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.04%)
2025-08-18 02:02:10.060 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第62/1000条: (113.568618,24.740583) -> (113.521811,24.75693)
2025-08-18 02:02:10.113 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:10.420 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第63/1000条: (114.322827,25.114172) -> (113.555835,24.801575)
2025-08-18 02:02:10.492 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.94%)
2025-08-18 02:02:10.808 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第64/1000条: (113.578678,24.723723) -> (113.732831,24.553703)
2025-08-18 02:02:10.885 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:11.195 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第65/1000条: (113.600175,24.673246) -> (114.086175,24.68701)
2025-08-18 02:02:11.288 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.64%)
2025-08-18 02:02:11.604 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第66/1000条: (113.595545,24.813051) -> (113.595394,24.807711)
2025-08-18 02:02:11.653 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:11.967 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第67/1000条: (113.611627,24.8042) -> (113.780645,25.027158)
2025-08-18 02:02:12.024 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.44%)
2025-08-18 02:02:12.336 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第68/1000条: (113.639323,24.69488) -> (113.730359,25.079344)
2025-08-18 02:02:12.418 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:12.727 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第69/1000条: (114.214035,24.860307) -> (114.297908,25.095937)
2025-08-18 02:02:12.811 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过44.63%)
2025-08-18 02:02:13.116 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第70/1000条: (113.593269,24.680576) -> (113.844071,25.232288)
2025-08-18 02:02:13.195 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.98%)
2025-08-18 02:02:13.505 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第71/1000条: (113.566906,24.538753) -> (113.346369,25.12481)
2025-08-18 02:02:13.580 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.32%)
2025-08-18 02:02:13.893 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第72/1000条: (113.604889,24.81119) -> (113.582493,24.783033)
2025-08-18 02:02:13.959 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.48%)
2025-08-18 02:02:14.271 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第73/1000条: (113.307433,24.776162) -> (113.362149,25.134651)
2025-08-18 02:02:14.350 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:14.661 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第74/1000条: (113.581583,25.076872) -> (113.600236,24.804537)
2025-08-18 02:02:14.737 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:15.047 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第75/1000条: (113.512655,24.747531) -> (113.347218,25.391851)
2025-08-18 02:02:15.149 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.97%)
2025-08-18 02:02:15.452 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第76/1000条: (113.599852,24.802513) -> (113.600829,24.781865)
2025-08-18 02:02:15.508 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.51605541815965%)
2025-08-18 02:02:15.813 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第77/1000条: (114.586363,25.259497) -> (114.058565,24.951936)
2025-08-18 02:02:15.876 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过99.0097133590167%)
2025-08-18 02:02:16.188 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第78/1000条: (113.821488,24.684006) -> (113.279017,24.777734)
2025-08-18 02:02:16.275 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.57%)
2025-08-18 02:02:16.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第79/1000条: (114.214035,24.860307) -> (114.213167,24.06088)
2025-08-18 02:02:16.695 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.44%)
2025-08-18 02:02:17.001 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第80/1000条: (113.600175,24.673246) -> (114.060164,24.950945)
2025-08-18 02:02:17.089 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.93%)
2025-08-18 02:02:17.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第81/1000条: (113.590579,24.803518) -> (113.523381,24.779982)
2025-08-18 02:02:17.458 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.538815157524837%)
2025-08-18 02:02:17.765 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第82/1000条: (113.433509,24.72345) -> (113.282211,24.773934)
2025-08-18 02:02:17.831 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.049088007992566%)
2025-08-18 02:02:18.138 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第83/1000条: (113.457207,24.728261) -> (113.346523,25.13173)
2025-08-18 02:02:18.207 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过45.21%)
2025-08-18 02:02:18.510 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第84/1000条: (113.551357,24.835894) -> (113.681038,24.773209)
2025-08-18 02:02:18.577 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过19.78%)
2025-08-18 02:02:18.883 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第85/1000条: (113.580549,24.739854) -> (113.595542,24.638279)
2025-08-18 02:02:18.942 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.62%)
2025-08-18 02:02:19.257 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第86/1000条: (113.594231,24.666791) -> (113.123728,25.038126)
2025-08-18 02:02:19.326 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.87%)
2025-08-18 02:02:19.629 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第87/1000条: (113.544043,24.763413) -> (114.30348,25.109367)
2025-08-18 02:02:19.698 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.43%)
2025-08-18 02:02:20.002 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第88/1000条: (113.29167,24.774663) -> (114.210862,24.048482)
2025-08-18 02:02:20.117 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:20.425 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第89/1000条: (114.479169,25.148869) -> (114.307464,25.121895)
2025-08-18 02:02:20.497 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:20.801 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第90/1000条: (113.343793,25.133114) -> (113.594152,24.8133)
2025-08-18 02:02:20.863 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.93%)
2025-08-18 02:02:21.175 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第91/1000条: (113.586858,24.817181) -> (113.274721,24.782942)
2025-08-18 02:02:21.231 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:21.546 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第92/1000条: (113.275042,24.776658) -> (113.532205,24.757414)
2025-08-18 02:02:21.605 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.71663910217897%)
2025-08-18 02:02:21.918 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第93/1000条: (114.713758,25.294158) -> (114.268916,25.068273)
2025-08-18 02:02:22.001 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:22.307 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第94/1000条: (113.58442,24.778843) -> (114.317904,25.121835)
2025-08-18 02:02:22.380 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.44%)
2025-08-18 02:02:22.697 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第95/1000条: (113.698505,24.767171) -> (113.180146,25.438707)
2025-08-18 02:02:22.779 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.88%)
2025-08-18 02:02:23.084 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第96/1000条: (113.580213,24.800478) -> (113.355593,25.129658)
2025-08-18 02:02:23.169 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.48%)
2025-08-18 02:02:23.473 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第97/1000条: (113.404361,24.870651) -> (113.556485,24.779326)
2025-08-18 02:02:23.553 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.77%)
2025-08-18 02:02:23.861 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第98/1000条: (113.615234,24.795495) -> (113.475749,25.108186)
2025-08-18 02:02:23.925 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过60.54%)
2025-08-18 02:02:24.232 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第99/1000条: (113.293688,24.775343) -> (113.573997,24.793932)
2025-08-18 02:02:24.300 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:24.606 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第100/1000条: (114.026327,25.185653) -> (114.620584,25.214766)
2025-08-18 02:02:24.706 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.25%)
2025-08-18 02:02:25.012 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第101/1000条: (113.616492,24.827861) -> (113.653088,24.700235)
2025-08-18 02:02:25.084 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:25.385 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第102/1000条: (114.069694,24.946767) -> (114.399033,25.180366)
2025-08-18 02:02:25.451 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.68774276748616%)
2025-08-18 02:02:25.759 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第103/1000条: (114.307452,25.115455) -> (114.307309,25.112802)
2025-08-18 02:02:25.830 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过0.6%)
2025-08-18 02:02:26.144 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第104/1000条: (113.374479,25.139548) -> (114.123321,24.355748)
2025-08-18 02:02:26.518 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:26.828 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第105/1000条: (114.06634,24.953742) -> (113.581517,24.807158)
2025-08-18 02:02:26.936 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.79%)
2025-08-18 02:02:27.246 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第106/1000条: (113.595524,24.680449) -> (113.427846,24.951118)
2025-08-18 02:02:27.319 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过39.71%)
2025-08-18 02:02:27.620 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第107/1000条: (113.56282,24.829124) -> (113.58208,24.75415)
2025-08-18 02:02:27.685 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.2%)
2025-08-18 02:02:27.992 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第108/1000条: (113.580219,24.827379) -> (113.74233,25.085314)
2025-08-18 02:02:28.073 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.34702044217691%)
2025-08-18 02:02:28.383 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第109/1000条: (113.077753,25.176666) -> (114.282022,24.083976)
2025-08-18 02:02:28.483 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:28.788 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第110/1000条: (113.592758,24.792533) -> (113.287864,24.776177)
2025-08-18 02:02:28.872 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.82%)
2025-08-18 02:02:29.178 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第111/1000条: (113.362417,25.222606) -> (113.461961,24.728928)
2025-08-18 02:02:29.266 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.54%)
2025-08-18 02:02:29.566 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第112/1000条: (113.611247,24.683199) -> (113.533513,24.786953)
2025-08-18 02:02:29.642 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过19.52%)
2025-08-18 02:02:29.957 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第113/1000条: (113.256871,25.455303) -> (113.733547,25.257812)
2025-08-18 02:02:30.060 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.03750450049547%)
2025-08-18 02:02:30.364 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第114/1000条: (113.615234,24.795495) -> (113.588044,24.804015)
2025-08-18 02:02:30.407 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.4%)
2025-08-18 02:02:30.718 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第115/1000条: (113.616492,24.827861) -> (114.310059,25.116844)
2025-08-18 02:02:30.790 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.74%)
2025-08-18 02:02:31.095 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第116/1000条: (113.551773,24.77057) -> (113.560328,24.8292)
2025-08-18 02:02:31.159 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.02%)
2025-08-18 02:02:31.469 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第117/1000条: (113.5078,24.760944) -> (113.523934,24.751943)
2025-08-18 02:02:31.511 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.44%)
2025-08-18 02:02:31.813 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第118/1000条: (113.585818,24.787762) -> (114.308046,24.117769)
2025-08-18 02:02:31.963 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过115.84%)
2025-08-18 02:02:32.271 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第119/1000条: (114.311628,25.109428) -> (114.072325,24.946285)
2025-08-18 02:02:32.343 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过46.40722498536071%)
2025-08-18 02:02:32.657 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第120/1000条: (113.569536,24.796436) -> (113.581662,24.79559)
2025-08-18 02:02:32.701 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.97%)
2025-08-18 02:02:33.001 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第121/1000条: (113.955875,25.330793) -> (113.584539,24.760549)
2025-08-18 02:02:33.092 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:33.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第122/1000条: (114.308845,25.116595) -> (114.049258,24.954539)
2025-08-18 02:02:33.459 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.07%)
2025-08-18 02:02:33.764 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第123/1000条: (113.387391,24.831489) -> (113.582163,24.774319)
2025-08-18 02:02:33.827 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过36.89%)
2025-08-18 02:02:34.139 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第124/1000条: (113.572474,24.800514) -> (113.291884,24.771297)
2025-08-18 02:02:34.209 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:34.513 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第125/1000条: (114.456557,25.258442) -> (114.077179,24.952419)
2025-08-18 02:02:34.591 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.92%)
2025-08-18 02:02:34.901 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第126/1000条: (113.634005,24.817828) -> (113.828252,24.373631)
2025-08-18 02:02:34.981 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.96%)
2025-08-18 02:02:35.290 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第127/1000条: (113.609192,24.683351) -> (113.556243,24.830729)
2025-08-18 02:02:35.372 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.55%)
2025-08-18 02:02:35.679 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第128/1000条: (112.925816,25.276824) -> (114.214888,24.060932)
2025-08-18 02:02:35.802 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:36.115 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第129/1000条: (114.496002,25.225061) -> (114.069303,24.94929)
2025-08-18 02:02:36.177 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.19%)
2025-08-18 02:02:36.487 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第130/1000条: (114.078911,24.94844) -> (114.1721,24.545918)
2025-08-18 02:02:36.578 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.2%)
2025-08-18 02:02:36.893 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第131/1000条: (113.639323,24.69488) -> (113.524565,24.787409)
2025-08-18 02:02:36.960 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.75%)
2025-08-18 02:02:37.270 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第132/1000条: (113.58834,24.796571) -> (113.582163,24.774319)
2025-08-18 02:02:37.327 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.0228076140432565%)
2025-08-18 02:02:37.641 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第133/1000条: (113.599852,24.802513) -> (113.343802,25.123828)
2025-08-18 02:02:37.721 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.79%)
2025-08-18 02:02:38.031 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第134/1000条: (113.609352,24.804867) -> (113.545994,24.738085)
2025-08-18 02:02:38.090 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.34%)
2025-08-18 02:02:38.402 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第135/1000条: (113.180508,25.437686) -> (114.062437,24.947545)
2025-08-18 02:02:38.505 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:38.808 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第136/1000条: (113.591325,24.644425) -> (113.605375,24.831731)
2025-08-18 02:02:38.885 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.1%)
2025-08-18 02:02:39.198 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第137/1000条: (113.60229,24.81692) -> (113.626033,24.692593)
2025-08-18 02:02:39.251 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:39.558 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第138/1000条: (114.311483,25.120881) -> (113.603523,24.793479)
2025-08-18 02:02:39.632 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.76%)
2025-08-18 02:02:39.932 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第139/1000条: (113.60339,24.820251) -> (113.129062,25.169007)
2025-08-18 02:02:40.018 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.42%)
2025-08-18 02:02:40.322 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第140/1000条: (113.073138,25.174875) -> (113.603412,24.575727)
2025-08-18 02:02:40.421 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.03%)
2025-08-18 02:02:40.727 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第141/1000条: (113.581009,24.801379) -> (113.294488,24.780894)
2025-08-18 02:02:40.816 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:41.128 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第142/1000条: (113.272826,24.772738) -> (113.653088,24.700235)
2025-08-18 02:02:41.225 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.87%)
2025-08-18 02:02:41.534 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第143/1000条: (114.079267,24.676201) -> (114.135928,24.922337)
2025-08-18 02:02:41.596 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:41.909 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第144/1000条: (113.46116,24.744966) -> (113.480285,24.739769)
2025-08-18 02:02:41.962 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.76%)
2025-08-18 02:02:42.263 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第145/1000条: (113.577784,24.801134) -> (113.287864,24.776177)
2025-08-18 02:02:42.341 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.11%)
2025-08-18 02:02:42.645 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第146/1000条: (113.814666,24.976133) -> (113.695969,24.765217)
2025-08-18 02:02:42.707 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.769730199074154%)
2025-08-18 02:02:43.008 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第147/1000条: (113.350871,25.136991) -> (113.454917,24.731762)
2025-08-18 02:02:43.083 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.28%)
2025-08-18 02:02:43.399 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第148/1000条: (113.057208,25.213304) -> (113.521811,24.75693)
2025-08-18 02:02:43.490 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.47%)
2025-08-18 02:02:43.800 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第149/1000条: (114.405841,25.172917) -> (113.560588,25.088858)
2025-08-18 02:02:43.880 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.0%)
2025-08-18 02:02:44.191 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第150/1000条: (113.578044,24.794004) -> (113.653088,24.700235)
2025-08-18 02:02:44.267 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.21%)
2025-08-18 02:02:44.567 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第151/1000条: (113.474268,25.161452) -> (113.473357,25.145196)
2025-08-18 02:02:44.617 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.273248280520498%)
2025-08-18 02:02:44.925 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第152/1000条: (114.335146,25.138737) -> (114.141064,24.916167)
2025-08-18 02:02:44.983 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.59%)
2025-08-18 02:02:45.293 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第153/1000条: (114.093378,24.732606) -> (113.099027,24.971237)
2025-08-18 02:02:45.409 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:45.712 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第154/1000条: (113.588587,24.818755) -> (113.565601,24.799805)
2025-08-18 02:02:45.783 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.14%)
2025-08-18 02:02:46.086 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第155/1000条: (113.697272,24.763683) -> (113.352943,25.168586)
2025-08-18 02:02:46.171 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.13%)
2025-08-18 02:02:46.477 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第156/1000条: (114.266103,25.093478) -> (113.599277,24.683307)
2025-08-18 02:02:46.573 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.36%)
2025-08-18 02:02:46.876 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第157/1000条: (114.308262,25.115691) -> (113.275042,24.776658)
2025-08-18 02:02:46.981 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.49%)
2025-08-18 02:02:47.294 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第158/1000条: (113.597019,24.78895) -> (113.799477,25.160434)
2025-08-18 02:02:47.378 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.81509495018717%)
2025-08-18 02:02:47.679 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第159/1000条: (113.027563,24.565083) -> (113.517586,24.751701)
2025-08-18 02:02:47.790 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.36%)
2025-08-18 02:02:48.098 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第160/1000条: (114.306068,25.119225) -> (114.693755,25.23113)
2025-08-18 02:02:48.164 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过44.48%)
2025-08-18 02:02:48.470 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第161/1000条: (113.279017,24.777734) -> (114.399033,25.180366)
2025-08-18 02:02:48.571 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.75%)
2025-08-18 02:02:48.871 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第162/1000条: (113.531246,24.756334) -> (113.582163,24.774319)
2025-08-18 02:02:48.927 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:49.241 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第163/1000条: (114.309424,25.154076) -> (114.069303,24.94929)
2025-08-18 02:02:49.317 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:49.629 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第164/1000条: (113.288146,24.780403) -> (114.13341,24.358857)
2025-08-18 02:02:49.726 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.86%)
2025-08-18 02:02:50.032 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第165/1000条: (113.607486,24.792439) -> (113.827897,25.055284)
2025-08-18 02:02:50.091 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.64999698465214%)
2025-08-18 02:02:50.404 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第166/1000条: (113.576261,24.804373) -> (113.573997,24.793932)
2025-08-18 02:02:50.458 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.8727797566981472%)
2025-08-18 02:02:50.765 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第167/1000条: (114.713758,25.294158) -> (113.616454,25.069895)
2025-08-18 02:02:50.884 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过112.48%)
2025-08-18 02:02:51.199 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第168/1000条: (113.674734,24.611325) -> (113.638439,25.078258)
2025-08-18 02:02:51.290 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.27%)
2025-08-18 02:02:51.607 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第169/1000条: (114.363107,25.29189) -> (113.587072,24.758847)
2025-08-18 02:02:51.687 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.04%)
2025-08-18 02:02:51.995 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第170/1000条: (113.596481,24.785717) -> (113.581276,24.75128)
2025-08-18 02:02:52.063 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.61%)
2025-08-18 02:02:52.370 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第171/1000条: (113.286791,25.485591) -> (113.279162,24.774448)
2025-08-18 02:02:52.474 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.7%)
2025-08-18 02:02:52.790 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第172/1000条: (113.610591,24.676035) -> (113.653088,24.700235)
2025-08-18 02:02:52.860 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.909102634994962%)
2025-08-18 02:02:53.165 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第173/1000条: (113.072692,25.181274) -> (114.153187,24.857181)
2025-08-18 02:02:53.256 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过118.8%)
2025-08-18 02:02:53.570 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第174/1000条: (114.617453,25.193391) -> (114.325444,24.922047)
2025-08-18 02:02:53.653 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.84%)
2025-08-18 02:02:53.958 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第175/1000条: (113.375987,25.342731) -> (113.352953,25.131954)
2025-08-18 02:02:54.035 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过39.74906766518426%)
2025-08-18 02:02:54.347 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第176/1000条: (113.599991,24.814949) -> (113.746279,25.088949)
2025-08-18 02:02:54.409 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过36.04%)
2025-08-18 02:02:54.724 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第177/1000条: (113.279245,24.778269) -> (113.528143,24.78718)
2025-08-18 02:02:54.810 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.886194854559314%)
2025-08-18 02:02:55.112 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第178/1000条: (113.620294,24.827684) -> (113.596707,24.824025)
2025-08-18 02:02:55.164 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.72%)
2025-08-18 02:02:55.470 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第179/1000条: (113.278081,24.771981) -> (114.514696,25.304417)
2025-08-18 02:02:55.587 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:55.892 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第180/1000条: (113.624317,24.965148) -> (113.609352,24.804867)
2025-08-18 02:02:55.948 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:56.250 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第181/1000条: (114.153187,24.857181) -> (114.212508,24.064395)
2025-08-18 02:02:56.543 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.76%)
2025-08-18 02:02:56.845 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第182/1000条: (113.581517,24.807158) -> (113.564574,24.799385)
2025-08-18 02:02:56.889 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.38%)
2025-08-18 02:02:57.200 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第183/1000条: (113.279245,24.778269) -> (114.128725,24.50975)
2025-08-18 02:02:57.286 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.38%)
2025-08-18 02:02:57.601 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第184/1000条: (113.369451,25.345652) -> (113.334596,25.427961)
2025-08-18 02:02:57.660 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过18.760287013589945%)
2025-08-18 02:02:57.961 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第185/1000条: (113.457207,24.728261) -> (114.151561,24.405267)
2025-08-18 02:02:58.040 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.31%)
2025-08-18 02:02:58.350 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第186/1000条: (113.57577,24.793558) -> (113.293688,24.775343)
2025-08-18 02:02:58.414 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:02:58.725 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第187/1000条: (113.599636,24.797348) -> (113.799477,25.160434)
2025-08-18 02:02:58.816 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.1%)
2025-08-18 02:02:59.129 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第188/1000条: (113.391777,25.312724) -> (113.588064,24.787474)
2025-08-18 02:02:59.244 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.83%)
2025-08-18 02:02:59.551 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第189/1000条: (113.533343,24.892676) -> (114.149023,24.011932)
2025-08-18 02:02:59.649 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:02:59.955 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第190/1000条: (113.837486,25.205873) -> (113.745712,25.03193)
2025-08-18 02:03:00.035 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过35.64%)
2025-08-18 02:03:00.342 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第191/1000条: (114.294274,25.107975) -> (114.315609,25.113869)
2025-08-18 02:03:00.391 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.43%)
2025-08-18 02:03:00.696 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第192/1000条: (114.063307,24.947049) -> (113.586601,24.780892)
2025-08-18 02:03:00.786 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.79%)
2025-08-18 02:03:01.098 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第193/1000条: (113.596344,24.800998) -> (113.600089,24.789588)
2025-08-18 02:03:01.155 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.14%)
2025-08-18 02:03:01.471 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第194/1000条: (113.468473,25.157958) -> (113.360766,25.107692)
2025-08-18 02:03:01.538 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.676298131814093%)
2025-08-18 02:03:01.849 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第195/1000条: (114.515723,25.273965) -> (114.258287,25.133531)
2025-08-18 02:03:01.918 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:02.224 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第196/1000条: (113.598486,24.787637) -> (113.533729,24.753516)
2025-08-18 02:03:02.277 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.9%)
2025-08-18 02:03:02.581 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第197/1000条: (113.827897,25.055284) -> (113.621871,24.821854)
2025-08-18 02:03:02.642 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.16542107367144%)
2025-08-18 02:03:02.953 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第198/1000条: (113.521762,24.75531) -> (113.588731,24.742704)
2025-08-18 02:03:03.009 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.76%)
2025-08-18 02:03:03.312 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第199/1000条: (113.523934,24.751943) -> (113.586887,24.807675)
2025-08-18 02:03:03.379 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:03.687 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第200/1000条: (113.461557,25.169422) -> (113.581682,24.803563)
2025-08-18 02:03:03.769 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.01%)
2025-08-18 02:03:04.076 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第201/1000条: (113.579177,24.793081) -> (114.075691,24.964826)
2025-08-18 02:03:04.161 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.51%)
2025-08-18 02:03:04.465 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第202/1000条: (114.357293,25.191263) -> (113.589946,24.809256)
2025-08-18 02:03:04.549 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.25%)
2025-08-18 02:03:04.855 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第203/1000条: (114.301585,25.110472) -> (113.581662,24.79559)
2025-08-18 02:03:04.945 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.45%)
2025-08-18 02:03:05.245 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第204/1000条: (113.609049,24.682381) -> (114.291377,25.113797)
2025-08-18 02:03:05.340 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.58%)
2025-08-18 02:03:05.650 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第205/1000条: (113.351508,25.128528) -> (113.833418,24.458941)
2025-08-18 02:03:05.751 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.86%)
2025-08-18 02:03:06.052 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第206/1000条: (113.596344,24.800998) -> (113.596441,24.786987)
2025-08-18 02:03:06.111 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.7041960859732987%)
2025-08-18 02:03:06.426 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第207/1000条: (113.934611,24.978389) -> (114.056929,24.945045)
2025-08-18 02:03:06.485 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.92%)
2025-08-18 02:03:06.797 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第208/1000条: (113.594513,24.827274) -> (113.600175,24.673246)
2025-08-18 02:03:06.867 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.468777385892952%)
2025-08-18 02:03:07.172 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第209/1000条: (113.592758,24.792533) -> (113.456023,24.727554)
2025-08-18 02:03:07.227 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.64798314977949%)
2025-08-18 02:03:07.529 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第210/1000条: (113.588099,24.791497) -> (113.57365,24.726094)
2025-08-18 02:03:07.596 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:07.910 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第211/1000条: (113.504393,24.91505) -> (113.832959,24.459079)
2025-08-18 02:03:08.003 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.29%)
2025-08-18 02:03:08.312 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第212/1000条: (114.065794,24.942697) -> (114.20146,24.053991)
2025-08-18 02:03:08.418 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.98%)
2025-08-18 02:03:08.732 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第213/1000条: (113.583417,24.797819) -> (113.530886,24.847659)
2025-08-18 02:03:08.800 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.46%)
2025-08-18 02:03:09.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第214/1000条: (113.512655,24.747531) -> (113.534303,24.784778)
2025-08-18 02:03:09.157 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.849191025524789%)
2025-08-18 02:03:09.460 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第215/1000条: (113.287563,24.778928) -> (113.552856,24.800859)
2025-08-18 02:03:09.536 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.9%)
2025-08-18 02:03:09.847 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第216/1000条: (113.595006,24.571516) -> (114.313807,25.117478)
2025-08-18 02:03:09.928 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过85.06%)
2025-08-18 02:03:10.239 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第217/1000条: (113.040821,25.286564) -> (113.347055,25.126315)
2025-08-18 02:03:10.327 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.65%)
2025-08-18 02:03:10.644 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第218/1000条: (113.272826,24.772738) -> (114.061127,24.9494)
2025-08-18 02:03:10.743 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.88%)
2025-08-18 02:03:11.049 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第219/1000条: (113.603188,24.792137) -> (113.745144,25.089252)
2025-08-18 02:03:11.131 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:11.440 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第220/1000条: (113.678238,24.773593) -> (114.227365,24.16453)
2025-08-18 02:03:11.572 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过105.61%)
2025-08-18 02:03:11.873 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第221/1000条: (114.014315,25.182799) -> (113.299272,24.757754)
2025-08-18 02:03:11.962 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:12.263 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第222/1000条: (113.58442,24.778843) -> (113.569692,24.798953)
2025-08-18 02:03:12.313 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.296741646409226%)
2025-08-18 02:03:12.623 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第223/1000条: (113.582764,24.777482) -> (114.617453,25.193391)
2025-08-18 02:03:12.725 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.54%)
2025-08-18 02:03:13.027 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第224/1000条: (113.291884,24.771297) -> (114.538944,25.173237)
2025-08-18 02:03:13.138 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:13.446 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第225/1000条: (113.581383,24.681744) -> (114.205697,24.057728)
2025-08-18 02:03:13.534 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过110.09%)
2025-08-18 02:03:13.837 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第226/1000条: (113.611426,24.847112) -> (113.588064,24.787474)
2025-08-18 02:03:13.902 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.5%)
2025-08-18 02:03:14.203 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第227/1000条: (113.577252,24.757727) -> (113.561667,24.780641)
2025-08-18 02:03:14.267 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.623675046743355%)
2025-08-18 02:03:14.576 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第228/1000条: (113.541735,24.799046) -> (113.593394,24.779889)
2025-08-18 02:03:14.644 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.75%)
2025-08-18 02:03:14.950 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第229/1000条: (113.846576,24.807736) -> (113.604993,24.677743)
2025-08-18 02:03:15.014 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过38.85%)
2025-08-18 02:03:15.327 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第230/1000条: (114.064067,24.948659) -> (113.541735,24.799046)
2025-08-18 02:03:15.414 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.39%)
2025-08-18 02:03:15.716 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第231/1000条: (113.358329,25.037371) -> (113.609684,24.697007)
2025-08-18 02:03:15.785 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.19%)
2025-08-18 02:03:16.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第232/1000条: (113.586163,24.805247) -> (113.432578,24.943395)
2025-08-18 02:03:16.174 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:16.477 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第233/1000条: (114.070623,24.944992) -> (114.356491,25.20927)
2025-08-18 02:03:16.561 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.76567037285146%)
2025-08-18 02:03:16.865 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第234/1000条: (113.346523,25.13173) -> (113.344814,25.126402)
2025-08-18 02:03:16.909 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:17.223 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第235/1000条: (114.197445,24.048847) -> (114.519762,24.176237)
2025-08-18 02:03:17.283 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.017625971670775%)
2025-08-18 02:03:17.596 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第236/1000条: (114.304841,25.106569) -> (114.050449,24.944344)
2025-08-18 02:03:17.649 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.23%)
2025-08-18 02:03:17.951 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第237/1000条: (114.322827,25.114172) -> (113.611627,24.8042)
2025-08-18 02:03:18.045 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.49%)
2025-08-18 02:03:18.354 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第238/1000条: (113.548193,24.769055) -> (113.865906,25.201329)
2025-08-18 02:03:18.452 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.07%)
2025-08-18 02:03:18.759 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第239/1000条: (114.063307,24.947049) -> (114.304841,25.106569)
2025-08-18 02:03:18.818 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.03%)
2025-08-18 02:03:19.131 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第240/1000条: (113.061544,25.282525) -> (113.269044,24.778633)
2025-08-18 02:03:19.218 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.52%)
2025-08-18 02:03:19.521 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第241/1000条: (113.287849,24.773466) -> (113.613632,24.803725)
2025-08-18 02:03:19.607 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.89%)
2025-08-18 02:03:19.910 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第242/1000条: (113.53129,24.893875) -> (113.821488,24.684006)
2025-08-18 02:03:19.980 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:20.285 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第243/1000条: (113.6212,24.748445) -> (113.377556,25.100644)
2025-08-18 02:03:20.368 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.47%)
2025-08-18 02:03:20.673 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第244/1000条: (113.504524,24.759227) -> (114.128911,24.479429)
2025-08-18 02:03:20.775 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.61%)
2025-08-18 02:03:21.081 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第245/1000条: (113.568939,24.8284) -> (113.15305,25.378223)
2025-08-18 02:03:21.170 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过85.15%)
2025-08-18 02:03:21.474 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第246/1000条: (113.069309,25.179806) -> (114.132683,24.353341)
2025-08-18 02:03:21.575 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:21.878 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第247/1000条: (113.856032,25.220024) -> (113.592613,24.770538)
2025-08-18 02:03:21.973 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:22.283 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第248/1000条: (114.496002,25.225061) -> (113.580213,24.800478)
2025-08-18 02:03:22.365 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.66%)
2025-08-18 02:03:22.669 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第249/1000条: (113.457207,24.728261) -> (113.569692,24.798953)
2025-08-18 02:03:22.747 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.49445736680124%)
2025-08-18 02:03:23.060 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第250/1000条: (114.386992,25.112732) -> (113.585818,24.787762)
2025-08-18 02:03:23.154 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.32%)
2025-08-18 02:03:23.461 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第251/1000条: (113.87611,25.307394) -> (113.74616,24.833962)
2025-08-18 02:03:23.552 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:23.868 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第252/1000条: (112.877377,25.279378) -> (113.568204,24.798225)
2025-08-18 02:03:23.970 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.1%)
2025-08-18 02:03:24.274 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第253/1000条: (113.351508,25.128528) -> (113.356817,25.123887)
2025-08-18 02:03:24.331 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过2.198002501142903%)
2025-08-18 02:03:24.633 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第254/1000条: (113.58453,24.765113) -> (113.598679,25.258883)
2025-08-18 02:03:24.718 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过85.70332424564495%)
2025-08-18 02:03:25.025 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第255/1000条: (113.533343,24.892676) -> (113.707052,24.767788)
2025-08-18 02:03:25.113 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.43%)
2025-08-18 02:03:25.414 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第256/1000条: (113.599192,25.254163) -> (113.759689,25.045149)
2025-08-18 02:03:25.492 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过45.03026469087384%)
2025-08-18 02:03:25.804 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第257/1000条: (113.021415,25.30728) -> (114.310059,25.116844)
2025-08-18 02:03:25.934 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:26.241 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第258/1000条: (113.618266,24.671456) -> (114.066471,24.94801)
2025-08-18 02:03:26.324 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.75%)
2025-08-18 02:03:26.628 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第259/1000条: (113.545994,24.738085) -> (114.305819,25.109109)
2025-08-18 02:03:26.725 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.57%)
2025-08-18 02:03:27.029 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第260/1000条: (113.378651,25.054932) -> (113.588967,24.797791)
2025-08-18 02:03:27.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:27.402 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第261/1000条: (113.612749,24.677089) -> (113.587012,24.825852)
2025-08-18 02:03:27.462 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过18.55%)
2025-08-18 02:03:27.763 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第262/1000条: (113.58238,24.775981) -> (113.077753,25.176666)
2025-08-18 02:03:27.838 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.82%)
2025-08-18 02:03:28.152 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第263/1000条: (113.281195,24.774317) -> (113.545994,24.738085)
2025-08-18 02:03:28.225 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.53%)
2025-08-18 02:03:28.525 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第264/1000条: (113.53129,24.893875) -> (114.112632,24.002858)
2025-08-18 02:03:28.631 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:28.946 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第265/1000条: (113.819424,24.788007) -> (113.460609,24.979835)
2025-08-18 02:03:29.030 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:29.335 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第266/1000条: (113.180146,25.438707) -> (113.474268,25.161452)
2025-08-18 02:03:29.429 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.09349867688971%)
2025-08-18 02:03:29.741 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第267/1000条: (113.607003,24.805804) -> (114.399033,25.180366)
2025-08-18 02:03:29.840 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.71%)
2025-08-18 02:03:30.146 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第268/1000条: (113.142074,24.539945) -> (113.727921,25.089071)
2025-08-18 02:03:30.227 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过115.26%)
2025-08-18 02:03:30.532 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第269/1000条: (113.357022,24.753232) -> (113.58238,24.775981)
2025-08-18 02:03:30.604 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.96%)
2025-08-18 02:03:30.905 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第270/1000条: (113.532154,24.893079) -> (114.278976,24.164484)
2025-08-18 02:03:31.014 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过116.63%)
2025-08-18 02:03:31.327 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第271/1000条: (114.407235,25.175169) -> (113.578044,24.794004)
2025-08-18 02:03:31.430 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.68%)
2025-08-18 02:03:31.731 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第272/1000条: (114.245011,25.259782) -> (113.512655,24.747531)
2025-08-18 02:03:31.843 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过101.22%)
2025-08-18 02:03:32.151 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第273/1000条: (113.6349,24.815838) -> (113.58714,25.075778)
2025-08-18 02:03:32.223 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:03:32.525 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第274/1000条: (114.076823,24.940924) -> (113.307881,24.758212)
2025-08-18 02:03:32.619 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.33%)
2025-08-18 02:03:32.932 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第275/1000条: (113.29167,24.774663) -> (114.303507,25.118506)
2025-08-18 02:03:33.051 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.78%)
2025-08-18 02:03:33.366 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第276/1000条: (114.211503,24.054395) -> (113.92477,24.153127)
2025-08-18 02:03:33.447 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过46.7736534187328%)
2025-08-18 02:03:33.757 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第277/1000条: (113.563664,24.797739) -> (113.346286,25.12527)
2025-08-18 02:03:33.839 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.57%)
2025-08-18 02:03:34.145 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第278/1000条: (114.251469,25.149927) -> (114.088929,24.923268)
2025-08-18 02:03:34.226 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.92%)
2025-08-18 02:03:34.536 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第279/1000条: (113.527495,24.556896) -> (113.290871,24.78015)
2025-08-18 02:03:34.655 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.39%)
2025-08-18 02:03:34.968 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第280/1000条: (113.610255,24.798332) -> (113.556485,24.779326)
2025-08-18 02:03:35.718 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.66%)
2025-08-18 02:03:36.027 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第281/1000条: (113.346397,25.119939) -> (114.058565,24.951936)
2025-08-18 02:03:37.741 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.79%)
2025-08-18 02:03:38.054 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第282/1000条: (113.070379,25.18224) -> (114.130325,24.349897)
2025-08-18 02:03:40.722 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:41.034 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第283/1000条: (113.61419,24.833026) -> (113.568939,24.8284)
2025-08-18 02:03:41.102 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过9.293131844507593%)
2025-08-18 02:03:41.409 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第284/1000条: (114.30889,25.114955) -> (114.163312,24.02242)
2025-08-18 02:03:41.538 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过114.11%)
2025-08-18 02:03:41.843 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第285/1000条: (114.555614,25.161188) -> (113.375987,25.342731)
2025-08-18 02:03:41.974 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:42.282 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第286/1000条: (113.591937,24.775475) -> (113.565601,24.799805)
2025-08-18 02:03:42.342 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.03%)
2025-08-18 02:03:42.656 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第287/1000条: (113.573582,24.800762) -> (114.650769,25.13671)
2025-08-18 02:03:42.744 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过116.03%)
2025-08-18 02:03:43.059 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第288/1000条: (113.287117,24.777588) -> (114.394942,25.17155)
2025-08-18 02:03:43.138 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.95%)
2025-08-18 02:03:43.452 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第289/1000条: (113.669818,24.607856) -> (113.714328,25.082479)
2025-08-18 02:03:43.515 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.53%)
2025-08-18 02:03:43.828 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第290/1000条: (113.15305,25.378223) -> (113.586884,24.807736)
2025-08-18 02:03:43.928 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过88.01%)
2025-08-18 02:03:44.232 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第291/1000条: (114.473623,25.116658) -> (113.591286,24.77007)
2025-08-18 02:03:44.308 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过101.27%)
2025-08-18 02:03:44.622 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第292/1000条: (113.499321,24.678969) -> (113.068204,25.188629)
2025-08-18 02:03:44.688 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.33%)
2025-08-18 02:03:44.999 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第293/1000条: (113.588099,24.791497) -> (113.580213,24.800478)
2025-08-18 02:03:45.056 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.512472994866135%)
2025-08-18 02:03:45.371 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第294/1000条: (114.141379,24.342357) -> (114.031112,24.248099)
2025-08-18 02:03:45.425 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.58444103619197%)
2025-08-18 02:03:45.733 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第295/1000条: (114.075796,24.953586) -> (114.309715,25.118263)
2025-08-18 02:03:45.801 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.717379326529475%)
2025-08-18 02:03:46.109 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第296/1000条: (113.596496,24.78139) -> (113.404361,24.870651)
2025-08-18 02:03:46.192 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.4%)
2025-08-18 02:03:46.502 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第297/1000条: (113.532154,24.893079) -> (114.042247,24.307109)
2025-08-18 02:03:46.605 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过85.3%)
2025-08-18 02:03:46.909 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第298/1000条: (114.310414,25.127279) -> (113.353186,25.130573)
2025-08-18 02:03:47.013 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过100.28%)
2025-08-18 02:03:47.313 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第299/1000条: (113.609207,24.680708) -> (113.590911,24.787542)
2025-08-18 02:03:47.377 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.0%)
2025-08-18 02:03:47.685 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第300/1000条: (114.245011,25.259782) -> (114.13477,24.35578)
2025-08-18 02:03:47.799 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过103.57%)
2025-08-18 02:03:48.102 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第301/1000条: (114.311628,25.109428) -> (113.733547,25.257812)
2025-08-18 02:03:48.170 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.32%)
2025-08-18 02:03:48.479 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第302/1000条: (113.355515,25.127355) -> (113.041933,25.289122)
2025-08-18 02:03:50.674 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.29%)
2025-08-18 02:03:50.981 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第303/1000条: (114.313807,25.117478) -> (114.480184,25.15007)
2025-08-18 02:03:51.113 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.83%)
2025-08-18 02:03:51.422 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第304/1000条: (113.430339,24.940683) -> (113.835703,23.98262)
2025-08-18 02:03:56.566 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:03:56.874 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第305/1000条: (113.58442,24.778843) -> (113.631825,24.817368)
2025-08-18 02:03:56.953 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.63%)
2025-08-18 02:03:57.263 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第306/1000条: (113.545276,24.769296) -> (113.595394,24.807711)
2025-08-18 02:03:57.312 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.29%)
2025-08-18 02:03:57.622 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第307/1000条: (113.595021,24.669227) -> (113.521458,24.675167)
2025-08-18 02:03:57.681 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.77%)
2025-08-18 02:03:57.996 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第308/1000条: (113.536028,24.897287) -> (113.377637,25.340258)
2025-08-18 02:03:58.077 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.03%)
2025-08-18 02:03:58.385 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第309/1000条: (113.474268,25.161452) -> (113.633119,24.690217)
2025-08-18 02:03:58.477 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.32%)
2025-08-18 02:03:58.777 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第310/1000条: (113.569263,24.79817) -> (113.589946,24.809256)
2025-08-18 02:03:58.839 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.14191685089564%)
2025-08-18 02:03:59.150 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第311/1000条: (113.52577,24.785277) -> (113.586705,24.787225)
2025-08-18 02:03:59.202 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.100070953078614%)
2025-08-18 02:03:59.509 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第312/1000条: (113.669771,24.608995) -> (113.646255,24.786377)
2025-08-18 02:04:02.451 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.55%)
2025-08-18 02:04:02.754 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第313/1000条: (114.306,25.116931) -> (113.600236,24.804537)
2025-08-18 02:04:07.026 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.69%)
2025-08-18 02:04:07.338 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第314/1000条: (114.377967,25.158692) -> (113.755981,25.091109)
2025-08-18 02:04:07.425 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.25%)
2025-08-18 02:04:07.728 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第315/1000条: (113.056804,25.283912) -> (113.549171,24.77019)
2025-08-18 02:04:07.832 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.44%)
2025-08-18 02:04:08.138 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第316/1000条: (113.52577,24.785277) -> (113.609192,24.683351)
2025-08-18 02:04:08.194 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.8%)
2025-08-18 02:04:08.495 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第317/1000条: (113.605244,24.809224) -> (113.532905,24.545201)
2025-08-18 02:04:08.572 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过45.405495213679586%)
2025-08-18 02:04:08.883 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第318/1000条: (114.311483,25.120881) -> (113.344096,25.122289)
2025-08-18 02:04:08.996 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.03%)
2025-08-18 02:04:09.303 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第319/1000条: (113.024308,25.300392) -> (113.356925,25.129027)
2025-08-18 02:04:09.392 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.06%)
2025-08-18 02:04:09.693 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第320/1000条: (114.456557,25.258442) -> (114.313807,25.117478)
2025-08-18 02:04:09.799 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.14%)
2025-08-18 02:04:10.113 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第321/1000条: (114.067355,24.946552) -> (114.314955,25.115597)
2025-08-18 02:04:10.191 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.71589060294953%)
2025-08-18 02:04:10.504 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第322/1000条: (113.561667,24.780641) -> (113.579595,24.794555)
2025-08-18 02:04:10.547 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.7379538377468418%)
2025-08-18 02:04:10.862 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第323/1000条: (113.593972,24.781168) -> (114.295215,25.12837)
2025-08-18 02:04:10.952 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.66%)
2025-08-18 02:04:11.252 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第324/1000条: (113.362149,25.134651) -> (114.11656,24.354095)
2025-08-18 02:04:11.358 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:11.659 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第325/1000条: (114.291377,25.113797) -> (114.104274,24.939692)
2025-08-18 02:04:11.726 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.5%)
2025-08-18 02:04:12.032 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第326/1000条: (113.588731,24.742704) -> (113.593281,24.784425)
2025-08-18 02:04:12.095 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.571730444021822%)
2025-08-18 02:04:12.401 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第327/1000条: (113.592182,24.809154) -> (113.605291,24.841961)
2025-08-18 02:04:12.452 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.2%)
2025-08-18 02:04:12.760 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第328/1000条: (113.607003,24.805804) -> (113.881511,24.271053)
2025-08-18 02:04:12.841 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.85%)
2025-08-18 02:04:13.155 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第329/1000条: (113.410968,24.894023) -> (113.347756,25.135714)
2025-08-18 02:04:13.228 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.76%)
2025-08-18 02:04:13.538 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第330/1000条: (113.609049,24.682381) -> (114.076363,24.950534)
2025-08-18 02:04:13.631 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.14%)
2025-08-18 02:04:13.931 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第331/1000条: (113.586341,24.764331) -> (113.363384,25.12655)
2025-08-18 02:04:13.997 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.95%)
2025-08-18 02:04:14.305 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第332/1000条: (113.603589,24.807449) -> (113.613272,24.82832)
2025-08-18 02:04:14.347 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:14.662 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第333/1000条: (113.58453,24.765113) -> (113.180508,25.437686)
2025-08-18 02:04:14.744 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.61%)
2025-08-18 02:04:15.053 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第334/1000条: (113.286791,25.485591) -> (113.634806,24.691875)
2025-08-18 02:04:15.162 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:15.476 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第335/1000条: (113.580549,24.739854) -> (113.58208,24.75415)
2025-08-18 02:04:15.532 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过2.17%)
2025-08-18 02:04:15.833 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第336/1000条: (113.364472,25.129208) -> (113.480285,24.739769)
2025-08-18 02:04:15.897 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.03%)
2025-08-18 02:04:16.204 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第337/1000条: (114.311628,25.109428) -> (114.202748,24.052514)
2025-08-18 02:04:16.331 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.26%)
2025-08-18 02:04:16.641 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第338/1000条: (113.640664,24.825489) -> (113.638439,25.078258)
2025-08-18 02:04:16.707 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:17.018 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第339/1000条: (114.426929,25.0811) -> (113.588064,24.787474)
2025-08-18 02:04:17.093 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.75%)
2025-08-18 02:04:17.408 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第340/1000条: (114.514696,25.304417) -> (114.309424,25.154076)
2025-08-18 02:04:17.494 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.57468700796755%)
2025-08-18 02:04:17.795 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第341/1000条: (114.313807,25.117478) -> (114.059076,24.26186)
2025-08-18 02:04:17.902 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过96.0%)
2025-08-18 02:04:18.202 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第342/1000条: (113.619938,24.686659) -> (113.602051,24.680082)
2025-08-18 02:04:18.266 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:18.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第343/1000条: (113.570588,24.800974) -> (113.543798,24.795991)
2025-08-18 02:04:18.629 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.726343325465056%)
2025-08-18 02:04:18.935 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第344/1000条: (113.523934,24.751943) -> (114.074085,24.946242)
2025-08-18 02:04:19.021 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.27%)
2025-08-18 02:04:19.326 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第345/1000条: (113.598245,24.788065) -> (113.578678,24.723723)
2025-08-18 02:04:19.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:19.698 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第346/1000条: (114.068978,24.952724) -> (113.279941,24.779111)
2025-08-18 02:04:19.770 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.04%)
2025-08-18 02:04:20.073 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第347/1000条: (113.439869,24.860542) -> (113.572681,24.7967)
2025-08-18 02:04:20.122 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.799262131825245%)
2025-08-18 02:04:20.429 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第348/1000条: (113.600428,24.689493) -> (113.616492,24.827861)
2025-08-18 02:04:20.496 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过19.36%)
2025-08-18 02:04:20.803 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第349/1000条: (113.596481,24.785717) -> (113.591129,24.827811)
2025-08-18 02:04:20.878 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过9.989371507014946%)
2025-08-18 02:04:21.178 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第350/1000条: (113.359292,25.133298) -> (113.640616,25.078599)
2025-08-18 02:04:21.258 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过37.86%)
2025-08-18 02:04:21.562 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第351/1000条: (114.028633,24.945119) -> (114.082915,24.944561)
2025-08-18 02:04:21.608 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.922963990664758%)
2025-08-18 02:04:21.923 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第352/1000条: (113.583576,24.751936) -> (114.453055,25.261355)
2025-08-18 02:04:22.055 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过91.63%)
2025-08-18 02:04:22.364 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第353/1000条: (113.287849,24.773466) -> (113.741358,25.31846)
2025-08-18 02:04:23.550 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过87.06%)
2025-08-18 02:04:23.860 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第354/1000条: (114.02538,25.185996) -> (114.496002,25.225061)
2025-08-18 02:04:23.928 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.65063560064037%)
2025-08-18 02:04:24.235 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第355/1000条: (113.616492,24.827861) -> (113.654013,24.70098)
2025-08-18 02:04:27.942 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.71529722474688%)
2025-08-18 02:04:28.242 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第356/1000条: (114.309479,25.116115) -> (113.569263,24.79817)
2025-08-18 02:04:28.315 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.5%)
2025-08-18 02:04:28.617 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第357/1000条: (113.532154,24.893079) -> (113.627971,25.111138)
2025-08-18 02:04:28.690 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:28.993 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第358/1000条: (114.065794,24.942697) -> (113.597872,24.798547)
2025-08-18 02:04:29.071 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.86%)
2025-08-18 02:04:29.385 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第359/1000条: (113.75721,25.05126) -> (113.595006,24.571516)
2025-08-18 02:04:29.454 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:29.755 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第360/1000条: (113.687868,24.737442) -> (114.065141,24.948065)
2025-08-18 02:04:29.843 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.92%)
2025-08-18 02:04:30.146 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第361/1000条: (114.30245,25.120018) -> (114.135265,24.356764)
2025-08-18 02:04:30.247 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.17%)
2025-08-18 02:04:30.553 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第362/1000条: (113.395364,24.831317) -> (113.590911,24.787542)
2025-08-18 02:04:30.618 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.81156707541544%)
2025-08-18 02:04:30.928 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第363/1000条: (113.674734,24.611325) -> (113.598252,24.674241)
2025-08-18 02:04:30.998 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:31.302 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第364/1000条: (113.700273,24.766681) -> (113.427912,24.944279)
2025-08-18 02:04:31.383 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.53%)
2025-08-18 02:04:31.691 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第365/1000条: (113.346649,25.131269) -> (113.282211,24.773934)
2025-08-18 02:04:31.757 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:32.065 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第366/1000条: (114.315148,25.116969) -> (113.856931,25.222093)
2025-08-18 02:04:32.174 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.62%)
2025-08-18 02:04:32.483 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第367/1000条: (113.279602,24.778606) -> (113.61419,24.833026)
2025-08-18 02:04:32.543 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过34.86%)
2025-08-18 02:04:32.844 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第368/1000条: (113.556852,24.849902) -> (113.740337,25.086177)
2025-08-18 02:04:32.920 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.04409407644242%)
2025-08-18 02:04:33.235 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第369/1000条: (114.074085,24.946242) -> (113.641896,24.638732)
2025-08-18 02:04:33.321 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.26%)
2025-08-18 02:04:33.625 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第370/1000条: (113.608731,24.794747) -> (114.039173,24.18568)
2025-08-18 02:04:33.717 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过101.58%)
2025-08-18 02:04:34.031 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第371/1000条: (113.388156,25.014333) -> (114.299425,25.110388)
2025-08-18 02:04:34.123 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过96.3%)
2025-08-18 02:04:34.425 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第372/1000条: (113.358771,25.109612) -> (113.799398,24.414106)
2025-08-18 02:04:34.528 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.11%)
2025-08-18 02:04:34.832 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第373/1000条: (113.602834,24.812793) -> (113.14235,24.917176)
2025-08-18 02:04:34.917 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.85%)
2025-08-18 02:04:35.232 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第374/1000条: (113.556485,24.779326) -> (113.621002,24.506921)
2025-08-18 02:04:35.306 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过36.87%)
2025-08-18 02:04:35.616 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第375/1000条: (113.595542,24.638279) -> (113.90219,24.157935)
2025-08-18 02:04:35.689 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过63.01%)
2025-08-18 02:04:36.003 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第376/1000条: (114.342211,25.318147) -> (113.595542,24.638279)
2025-08-18 02:04:36.091 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.26%)
2025-08-18 02:04:36.407 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第377/1000条: (113.674803,24.605205) -> (114.073046,24.957249)
2025-08-18 02:04:36.492 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.47%)
2025-08-18 02:04:36.796 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第378/1000条: (113.11442,25.238855) -> (113.353186,25.130573)
2025-08-18 02:04:36.872 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.21691972019261%)
2025-08-18 02:04:37.184 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第379/1000条: (113.600174,24.787999) -> (113.774273,24.916178)
2025-08-18 02:04:37.247 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:37.558 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第380/1000条: (113.11442,25.238855) -> (113.570588,24.800974)
2025-08-18 02:04:37.659 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.87%)
2025-08-18 02:04:37.963 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第381/1000条: (114.32763,24.9223) -> (114.313928,25.121581)
2025-08-18 02:04:38.055 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过46.24%)
2025-08-18 02:04:38.369 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第382/1000条: (114.318552,25.112633) -> (114.153573,24.903394)
2025-08-18 02:04:38.447 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.22%)
2025-08-18 02:04:38.754 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第383/1000条: (114.081799,24.950213) -> (114.291377,25.113797)
2025-08-18 02:04:38.811 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.01%)
2025-08-18 02:04:39.113 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第384/1000条: (113.58508,24.798956) -> (113.588044,24.804015)
2025-08-18 02:04:39.171 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.9684221993695497%)
2025-08-18 02:04:39.486 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第385/1000条: (113.581383,24.681744) -> (113.389862,25.131521)
2025-08-18 02:04:39.578 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.46%)
2025-08-18 02:04:39.891 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第386/1000条: (113.590201,24.797902) -> (113.774273,24.916178)
2025-08-18 02:04:39.946 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:40.250 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第387/1000条: (113.569352,24.80258) -> (113.033072,25.293054)
2025-08-18 02:04:40.348 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.61%)
2025-08-18 02:04:40.654 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第388/1000条: (114.297908,25.095937) -> (113.605337,24.682306)
2025-08-18 02:04:40.727 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.49%)
2025-08-18 02:04:41.029 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第389/1000条: (114.203622,24.05473) -> (114.115581,23.995913)
2025-08-18 02:04:41.102 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过18.121958436079165%)
2025-08-18 02:04:41.402 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第390/1000条: (113.306177,24.744979) -> (113.288218,24.779164)
2025-08-18 02:04:41.448 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.24%)
2025-08-18 02:04:41.762 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第391/1000条: (113.611426,24.847112) -> (113.270828,25.174287)
2025-08-18 02:04:41.844 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.99%)
2025-08-18 02:04:42.153 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第392/1000条: (113.639323,24.69488) -> (113.578678,24.723723)
2025-08-18 02:04:42.220 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:42.533 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第393/1000条: (114.079267,24.676201) -> (113.618841,25.09516)
2025-08-18 02:04:43.212 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.91%)
2025-08-18 02:04:43.524 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第394/1000条: (113.367685,25.105016) -> (113.467517,25.151848)
2025-08-18 02:04:43.911 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:44.213 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第395/1000条: (113.598147,24.787514) -> (114.183063,24.044341)
2025-08-18 02:04:44.312 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:44.620 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第396/1000条: (113.346523,25.13173) -> (113.349905,25.136256)
2025-08-18 02:04:44.981 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.111379822295592%)
2025-08-18 02:04:45.287 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第397/1000条: (114.310508,25.070864) -> (113.583034,24.784894)
2025-08-18 02:04:45.385 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.0%)
2025-08-18 02:04:45.689 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第398/1000条: (113.565042,24.849047) -> (113.857934,25.216371)
2025-08-18 02:04:45.862 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:46.173 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第399/1000条: (114.601448,25.262963) -> (113.258284,25.455503)
2025-08-18 02:04:46.328 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:46.638 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第400/1000条: (113.590988,24.77208) -> (113.539613,24.757645)
2025-08-18 02:04:46.932 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.73%)
2025-08-18 02:04:47.245 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第401/1000条: (113.089315,24.921496) -> (113.345016,25.129956)
2025-08-18 02:04:47.619 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.6%)
2025-08-18 02:04:47.932 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第402/1000条: (113.603088,24.788267) -> (113.592758,24.792533)
2025-08-18 02:04:47.991 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.79%)
2025-08-18 02:04:48.304 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第403/1000条: (114.313807,25.117478) -> (113.627971,25.111138)
2025-08-18 02:04:48.393 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.16%)
2025-08-18 02:04:48.695 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第404/1000条: (113.761405,24.777583) -> (114.214444,24.055257)
2025-08-18 02:04:48.834 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:49.147 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第405/1000条: (113.57543,24.823916) -> (114.302406,25.108696)
2025-08-18 02:04:49.235 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.15%)
2025-08-18 02:04:49.536 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第406/1000条: (113.972193,24.63009) -> (113.277844,24.778268)
2025-08-18 02:04:49.611 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.07%)
2025-08-18 02:04:49.912 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第407/1000条: (113.555835,24.801575) -> (113.83363,24.457527)
2025-08-18 02:04:49.995 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.46%)
2025-08-18 02:04:50.301 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第408/1000条: (113.375693,25.126004) -> (113.559505,24.91663)
2025-08-18 02:04:50.361 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:50.676 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第409/1000条: (114.306068,25.119225) -> (114.310006,25.112769)
2025-08-18 02:04:50.730 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.33%)
2025-08-18 02:04:51.035 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第410/1000条: (113.545597,24.771042) -> (114.199332,24.046063)
2025-08-18 02:04:51.147 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:51.451 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第411/1000条: (114.542513,25.244804) -> (114.071974,24.9498)
2025-08-18 02:04:51.512 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.53%)
2025-08-18 02:04:51.813 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第412/1000条: (113.421515,25.106846) -> (113.586884,24.807736)
2025-08-18 02:04:51.874 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:52.185 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第413/1000条: (113.598147,24.787514) -> (113.577252,24.757727)
2025-08-18 02:04:52.232 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.32%)
2025-08-18 02:04:52.541 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第414/1000条: (113.045956,25.283825) -> (113.609684,24.697007)
2025-08-18 02:04:52.638 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.53%)
2025-08-18 02:04:52.946 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第415/1000条: (113.499321,24.678969) -> (113.354649,25.132308)
2025-08-18 02:04:53.035 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.32%)
2025-08-18 02:04:53.346 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第416/1000条: (113.17706,25.439784) -> (113.588064,24.787474)
2025-08-18 02:04:53.438 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.05%)
2025-08-18 02:04:53.738 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第417/1000条: (113.0448,25.08635) -> (113.432578,24.943395)
2025-08-18 02:04:53.797 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.7%)
2025-08-18 02:04:54.111 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第418/1000条: (113.619938,24.686659) -> (113.274721,24.782942)
2025-08-18 02:04:54.256 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过35.13%)
2025-08-18 02:04:54.562 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第419/1000条: (113.576893,24.819747) -> (114.135354,24.358123)
2025-08-18 02:04:54.669 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.06%)
2025-08-18 02:04:54.971 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第420/1000条: (113.060297,25.283954) -> (113.625113,25.108762)
2025-08-18 02:04:55.045 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.99%)
2025-08-18 02:04:55.360 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第421/1000条: (113.577784,24.801134) -> (113.765271,25.101996)
2025-08-18 02:04:55.440 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.79%)
2025-08-18 02:04:55.747 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第422/1000条: (113.522061,24.748939) -> (113.563664,24.797739)
2025-08-18 02:04:55.815 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:04:56.121 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第423/1000条: (113.556813,24.851372) -> (113.591129,24.827811)
2025-08-18 02:04:56.167 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.83%)
2025-08-18 02:04:56.479 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第424/1000条: (113.60219,24.815619) -> (113.627971,25.111138)
2025-08-18 02:04:56.557 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.816221753780994%)
2025-08-18 02:04:56.868 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第425/1000条: (113.378779,25.342632) -> (113.565536,24.797884)
2025-08-18 02:04:56.951 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.98%)
2025-08-18 02:04:57.255 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第426/1000条: (113.070001,25.177635) -> (113.041933,25.289122)
2025-08-18 02:04:57.312 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.206522544639387%)
2025-08-18 02:04:57.615 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第427/1000条: (113.595401,24.811557) -> (113.577252,24.757727)
2025-08-18 02:04:57.666 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.93%)
2025-08-18 02:04:57.972 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第428/1000条: (114.418795,25.182877) -> (113.58783,24.756434)
2025-08-18 02:04:58.066 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.08%)
2025-08-18 02:04:58.377 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第429/1000条: (113.603412,24.575727) -> (113.556243,24.830729)
2025-08-18 02:04:58.455 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.26%)
2025-08-18 02:04:58.765 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第430/1000条: (113.600319,24.839798) -> (114.210862,24.048482)
2025-08-18 02:04:59.170 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:59.481 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第431/1000条: (114.727913,25.245015) -> (114.149023,24.011932)
2025-08-18 02:04:59.592 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:04:59.899 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第432/1000条: (114.311628,25.109428) -> (114.086175,24.68701)
2025-08-18 02:04:59.981 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:00.291 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第433/1000条: (113.622403,24.671573) -> (114.317019,25.12043)
2025-08-18 02:05:00.369 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.71%)
2025-08-18 02:05:00.679 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第434/1000条: (113.509863,25.285099) -> (113.374479,25.139548)
2025-08-18 02:05:00.753 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.63134491924864%)
2025-08-18 02:05:01.055 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第435/1000条: (113.289049,24.773981) -> (114.135354,24.358123)
2025-08-18 02:05:01.158 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.17%)
2025-08-18 02:05:01.464 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第436/1000条: (113.595023,24.673454) -> (113.819598,24.790325)
2025-08-18 02:05:01.527 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.47%)
2025-08-18 02:05:01.841 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第437/1000条: (113.367635,25.12753) -> (113.021688,25.304575)
2025-08-18 02:05:01.908 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.23%)
2025-08-18 02:05:02.212 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第438/1000条: (114.303507,25.118506) -> (114.321163,25.117883)
2025-08-18 02:05:02.264 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.67%)
2025-08-18 02:05:02.570 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第439/1000条: (113.346747,25.137237) -> (113.578603,24.805405)
2025-08-18 02:05:02.661 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.99%)
2025-08-18 02:05:02.976 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第440/1000条: (113.605636,24.675586) -> (114.21776,24.061352)
2025-08-18 02:05:03.072 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过108.54%)
2025-08-18 02:05:03.381 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第441/1000条: (113.586501,24.784123) -> (114.199906,24.051526)
2025-08-18 02:05:03.481 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过118.1%)
2025-08-18 02:05:03.788 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第442/1000条: (114.037085,24.245208) -> (113.833123,24.459578)
2025-08-18 02:05:03.868 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:04.178 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第443/1000条: (113.433509,24.72345) -> (113.073138,25.174875)
2025-08-18 02:05:04.283 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.61%)
2025-08-18 02:05:04.598 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第444/1000条: (113.611426,24.847112) -> (113.281195,24.774317)
2025-08-18 02:05:04.671 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.04%)
2025-08-18 02:05:04.975 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第445/1000条: (113.147422,24.987141) -> (113.070001,25.177635)
2025-08-18 02:05:05.020 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过34.86856382239807%)
2025-08-18 02:05:05.334 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第446/1000条: (113.654013,24.70098) -> (113.594193,24.782319)
2025-08-18 02:05:05.405 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.80122172052884%)
2025-08-18 02:05:05.709 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第447/1000条: (113.569259,25.000984) -> (113.536054,24.752073)
2025-08-18 02:05:05.780 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.07%)
2025-08-18 02:05:06.082 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第448/1000条: (113.274721,24.782942) -> (113.277844,24.778268)
2025-08-18 02:05:06.132 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过2.431675986384671%)
2025-08-18 02:05:06.441 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第449/1000条: (113.599135,24.523417) -> (113.892597,25.136844)
2025-08-18 02:05:06.518 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.78510965613646%)
2025-08-18 02:05:06.830 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第450/1000条: (113.413756,24.832092) -> (113.832418,24.457734)
2025-08-18 02:05:06.926 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.75%)
2025-08-18 02:05:07.235 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第451/1000条: (114.153573,24.903394) -> (113.278651,24.769408)
2025-08-18 02:05:07.320 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.0%)
2025-08-18 02:05:07.625 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第452/1000条: (113.446984,24.751947) -> (113.572474,24.800514)
2025-08-18 02:05:07.693 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:07.997 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第453/1000条: (114.145375,24.919113) -> (113.024308,25.300392)
2025-08-18 02:05:08.351 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:08.656 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第454/1000条: (113.272747,24.777425) -> (113.31406,24.756078)
2025-08-18 02:05:08.714 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.415423689963122%)
2025-08-18 02:05:09.028 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第455/1000条: (113.302097,24.758178) -> (113.578992,24.798214)
2025-08-18 02:05:09.089 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.91%)
2025-08-18 02:05:09.403 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第456/1000条: (113.279017,24.777734) -> (113.559505,24.91663)
2025-08-18 02:05:09.466 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过39.78%)
2025-08-18 02:05:09.773 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第457/1000条: (114.59023,25.259568) -> (114.604988,25.263226)
2025-08-18 02:05:09.828 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.563402264074903%)
2025-08-18 02:05:10.133 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第458/1000条: (113.521458,24.675167) -> (113.466034,25.153217)
2025-08-18 02:05:10.197 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.47%)
2025-08-18 02:05:10.503 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第459/1000条: (113.586501,24.784123) -> (113.230865,24.622416)
2025-08-18 02:05:10.636 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.86%)
2025-08-18 02:05:10.952 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第460/1000条: (113.581565,24.807145) -> (113.57577,24.793558)
2025-08-18 02:05:11.103 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:11.405 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第461/1000条: (113.274721,24.782942) -> (113.510203,24.629285)
2025-08-18 02:05:11.485 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.66%)
2025-08-18 02:05:11.795 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第462/1000条: (114.126839,24.522412) -> (114.029106,24.399441)
2025-08-18 02:05:11.867 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:12.169 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第463/1000条: (113.599146,24.774166) -> (113.285637,25.011526)
2025-08-18 02:05:12.249 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.1%)
2025-08-18 02:05:12.558 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第464/1000条: (113.524565,24.787409) -> (113.378651,25.054932)
2025-08-18 02:05:12.634 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:12.949 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第465/1000条: (113.410968,24.894023) -> (113.367046,25.114849)
2025-08-18 02:05:13.000 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.47%)
2025-08-18 02:05:13.311 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第466/1000条: (114.620584,25.214766) -> (113.598549,24.669555)
2025-08-18 02:05:13.393 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过109.71%)
2025-08-18 02:05:13.698 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第467/1000条: (114.592001,25.257126) -> (113.355101,25.116801)
2025-08-18 02:05:13.783 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:14.087 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第468/1000条: (113.25885,25.456409) -> (114.196068,24.052097)
2025-08-18 02:05:14.231 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:14.538 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第469/1000条: (114.294455,25.113379) -> (113.352953,25.131954)
2025-08-18 02:05:14.639 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过96.49%)
2025-08-18 02:05:14.942 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第470/1000条: (114.225045,25.059173) -> (114.306562,25.110817)
2025-08-18 02:05:15.008 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:15.315 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第471/1000条: (113.521811,24.75693) -> (114.140836,24.915321)
2025-08-18 02:05:15.387 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.66%)
2025-08-18 02:05:15.693 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第472/1000条: (113.275042,24.776658) -> (113.601789,24.793007)
2025-08-18 02:05:15.782 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过34.85%)
2025-08-18 02:05:16.082 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第473/1000条: (113.599378,24.780932) -> (113.590244,24.788504)
2025-08-18 02:05:16.144 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过2.4499691670694883%)
2025-08-18 02:05:16.455 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第474/1000条: (114.560303,25.16288) -> (113.606633,24.843078)
2025-08-18 02:05:16.546 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.33%)
2025-08-18 02:05:16.848 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第475/1000条: (113.523934,24.751943) -> (113.543727,24.766491)
2025-08-18 02:05:16.888 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.699055047960638%)
2025-08-18 02:05:17.190 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第476/1000条: (114.056929,24.945045) -> (113.762267,25.045032)
2025-08-18 02:05:17.261 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.3%)
2025-08-18 02:05:17.575 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第477/1000条: (114.128956,25.018742) -> (113.882349,24.273262)
2025-08-18 02:05:17.673 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过88.4%)
2025-08-18 02:05:17.982 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第478/1000条: (114.211297,24.860204) -> (114.124432,24.516945)
2025-08-18 02:05:18.064 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.71%)
2025-08-18 02:05:18.372 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第479/1000条: (113.532154,24.893079) -> (113.600829,24.781865)
2025-08-18 02:05:18.440 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.09%)
2025-08-18 02:05:18.749 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第480/1000条: (113.322701,24.739501) -> (113.524565,24.787409)
2025-08-18 02:05:18.806 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.82%)
2025-08-18 02:05:19.120 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第481/1000条: (113.743641,25.089059) -> (113.597019,24.78895)
2025-08-18 02:05:19.183 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:19.491 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第482/1000条: (114.057889,24.950146) -> (113.021595,25.314378)
2025-08-18 02:05:19.603 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过107.4%)
2025-08-18 02:05:19.914 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第483/1000条: (113.504393,24.91505) -> (113.287117,24.777588)
2025-08-18 02:05:19.983 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:20.287 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第484/1000条: (114.054773,24.95209) -> (114.204801,24.055997)
2025-08-18 02:05:20.404 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过91.84%)
2025-08-18 02:05:20.708 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第485/1000条: (113.539613,24.757645) -> (113.30287,24.757609)
2025-08-18 02:05:20.763 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.81607851708763%)
2025-08-18 02:05:21.067 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第486/1000条: (113.6349,24.815838) -> (113.60763,24.828038)
2025-08-18 02:05:21.113 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:21.423 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第487/1000条: (113.392661,25.085509) -> (113.364472,25.129208)
2025-08-18 02:05:21.465 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.48%)
2025-08-18 02:05:21.767 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第488/1000条: (114.307626,25.110196) -> (113.916758,24.148198)
2025-08-18 02:05:21.855 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过115.06%)
2025-08-18 02:05:22.159 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第489/1000条: (113.598252,24.674241) -> (114.129707,24.361301)
2025-08-18 02:05:22.242 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.89%)
2025-08-18 02:05:22.548 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第490/1000条: (114.311483,25.120881) -> (114.318552,25.112633)
2025-08-18 02:05:22.598 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.9914272444268464%)
2025-08-18 02:05:22.907 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第491/1000条: (114.514696,25.304417) -> (114.100904,24.952666)
2025-08-18 02:05:22.984 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.21%)
2025-08-18 02:05:23.297 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第492/1000条: (113.350575,25.130598) -> (114.196068,24.052097)
2025-08-18 02:05:23.425 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:23.731 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第493/1000条: (113.29167,24.774663) -> (113.608291,24.679423)
2025-08-18 02:05:23.807 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.46%)
2025-08-18 02:05:24.119 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第494/1000条: (114.306068,25.119225) -> (113.916758,24.148198)
2025-08-18 02:05:24.202 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过114.78%)
2025-08-18 02:05:24.508 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第495/1000条: (113.605797,24.791149) -> (113.8248,24.967731)
2025-08-18 02:05:24.809 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.15170402961343%)
2025-08-18 02:05:25.122 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第496/1000条: (113.58508,24.798956) -> (113.148759,24.53581)
2025-08-18 02:05:25.218 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.44340337474218%)
2025-08-18 02:05:25.528 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第497/1000条: (113.592071,24.782343) -> (113.59471,24.68447)
2025-08-18 02:05:25.590 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:25.902 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第498/1000条: (113.29167,24.774663) -> (114.350839,25.164156)
2025-08-18 02:05:26.020 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过100.11%)
2025-08-18 02:05:26.321 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第499/1000条: (113.348845,25.130124) -> (114.196068,24.052097)
2025-08-18 02:05:26.441 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:26.743 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第500/1000条: (114.064067,24.948659) -> (114.30889,25.114955)
2025-08-18 02:05:26.807 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过46.38470071448004%)
2025-08-18 02:05:27.118 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第501/1000条: (114.311628,25.109428) -> (113.061544,25.282525)
2025-08-18 02:05:27.295 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:27.602 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第502/1000条: (113.602105,24.791121) -> (113.749813,25.089747)
2025-08-18 02:05:27.682 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.22%)
2025-08-18 02:05:27.995 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第503/1000条: (113.021688,25.304575) -> (113.129062,25.169007)
2025-08-18 02:05:28.046 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.38%)
2025-08-18 02:05:28.353 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第504/1000条: (114.590166,25.198902) -> (113.581276,24.75128)
2025-08-18 02:05:28.432 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过108.86%)
2025-08-18 02:05:28.739 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第505/1000条: (113.697143,24.76541) -> (113.674803,24.605205)
2025-08-18 02:05:28.814 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.65%)
2025-08-18 02:05:29.128 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第506/1000条: (113.534303,24.784778) -> (114.133899,24.35324)
2025-08-18 02:05:29.227 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.98%)
2025-08-18 02:05:29.534 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第507/1000条: (113.714328,25.082479) -> (113.594193,24.782319)
2025-08-18 02:05:29.614 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:29.924 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第508/1000条: (113.571232,25.002234) -> (113.674803,24.605205)
2025-08-18 02:05:30.013 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.91%)
2025-08-18 02:05:30.314 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第509/1000条: (113.59917,24.788866) -> (113.569692,24.798953)
2025-08-18 02:05:30.376 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.94%)
2025-08-18 02:05:30.686 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第510/1000条: (113.6349,24.815838) -> (114.210862,24.048482)
2025-08-18 02:05:30.803 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:31.104 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第511/1000条: (114.305909,25.110592) -> (114.353523,25.285992)
2025-08-18 02:05:31.169 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:31.478 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第512/1000条: (114.31875,25.108158) -> (113.356817,25.123887)
2025-08-18 02:05:31.566 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过101.36%)
2025-08-18 02:05:31.870 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第513/1000条: (113.344959,25.121648) -> (113.467517,25.151848)
2025-08-18 02:05:31.931 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.43%)
2025-08-18 02:05:32.239 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第514/1000条: (113.059706,25.279947) -> (113.454639,25.15278)
2025-08-18 02:05:32.334 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.65%)
2025-08-18 02:05:32.644 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第515/1000条: (113.040821,25.286564) -> (114.307626,25.110196)
2025-08-18 02:05:32.793 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:33.109 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第516/1000条: (113.595841,24.539097) -> (113.512655,24.747531)
2025-08-18 02:05:33.166 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.15%)
2025-08-18 02:05:33.466 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第517/1000条: (114.278287,25.097139) -> (114.515723,25.273965)
2025-08-18 02:05:33.526 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.9943284781765%)
2025-08-18 02:05:33.843 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第518/1000条: (114.071988,24.94872) -> (114.313807,25.117478)
2025-08-18 02:05:33.914 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过46.31066902089154%)
2025-08-18 02:05:34.216 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第519/1000条: (113.357963,25.125335) -> (113.068204,25.188629)
2025-08-18 02:05:34.296 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过40.38%)
2025-08-18 02:05:34.607 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第520/1000条: (113.589946,24.809256) -> (113.563022,24.96761)
2025-08-18 02:05:34.669 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.86%)
2025-08-18 02:05:34.981 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第521/1000条: (113.555266,24.855565) -> (113.346286,25.12527)
2025-08-18 02:05:35.064 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:35.369 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第522/1000条: (113.582764,24.777482) -> (113.579595,24.794555)
2025-08-18 02:05:35.427 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.409384898659332%)
2025-08-18 02:05:35.727 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第523/1000条: (113.410795,25.111289) -> (113.538386,24.754911)
2025-08-18 02:05:35.795 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:36.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第524/1000条: (113.58063,24.747596) -> (113.286887,24.776535)
2025-08-18 02:05:36.173 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.07%)
2025-08-18 02:05:36.475 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第525/1000条: (113.293668,24.772292) -> (113.616689,24.673938)
2025-08-18 02:05:36.536 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过34.57%)
2025-08-18 02:05:36.849 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第526/1000条: (113.582903,24.743613) -> (114.289174,25.106499)
2025-08-18 02:05:36.921 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.81%)
2025-08-18 02:05:37.223 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第527/1000条: (113.616689,24.673938) -> (114.214444,24.055257)
2025-08-18 02:05:37.304 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.3%)
2025-08-18 02:05:37.611 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第528/1000条: (113.597271,24.787734) -> (113.579118,24.584581)
2025-08-18 02:05:37.687 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.31%)
2025-08-18 02:05:37.989 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第529/1000条: (114.213624,24.870219) -> (114.130325,24.349897)
2025-08-18 02:05:38.078 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.21%)
2025-08-18 02:05:38.392 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第530/1000条: (113.605666,24.790311) -> (113.37859,25.327282)
2025-08-18 02:05:38.491 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.71%)
2025-08-18 02:05:38.801 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第531/1000条: (113.512655,24.747531) -> (113.58508,24.798956)
2025-08-18 02:05:38.853 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:39.161 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第532/1000条: (114.364493,25.193861) -> (113.518064,24.751259)
2025-08-18 02:05:39.244 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.04%)
2025-08-18 02:05:39.553 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第533/1000条: (113.573997,24.793932) -> (113.231143,24.6236)
2025-08-18 02:05:39.632 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:39.945 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第534/1000条: (113.521811,24.75693) -> (113.587012,24.825852)
2025-08-18 02:05:40.010 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.07%)
2025-08-18 02:05:40.321 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第535/1000条: (113.529925,24.54474) -> (113.294488,24.780894)
2025-08-18 02:05:40.383 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.22%)
2025-08-18 02:05:40.696 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第536/1000条: (113.475749,25.108186) -> (113.626033,24.692593)
2025-08-18 02:05:40.787 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.4%)
2025-08-18 02:05:41.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第537/1000条: (114.114166,24.921534) -> (113.594513,24.827274)
2025-08-18 02:05:41.185 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.22%)
2025-08-18 02:05:41.491 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第538/1000条: (113.345696,25.122337) -> (113.720552,25.087685)
2025-08-18 02:05:41.556 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.79%)
2025-08-18 02:05:41.867 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第539/1000条: (113.54384,24.764802) -> (113.579595,24.794555)
2025-08-18 02:05:41.920 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.271495176739043%)
2025-08-18 02:05:42.224 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第540/1000条: (113.527045,24.676947) -> (114.026327,25.185653)
2025-08-18 02:05:42.298 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.5863766316331%)
2025-08-18 02:05:42.611 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第541/1000条: (113.061729,25.284353) -> (114.290709,25.120302)
2025-08-18 02:05:42.711 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:43.015 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第542/1000条: (113.531246,24.756334) -> (113.595139,24.711635)
2025-08-18 02:05:43.080 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.42%)
2025-08-18 02:05:43.386 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第543/1000条: (113.934611,24.978389) -> (114.30245,25.120018)
2025-08-18 02:05:43.475 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过61.70809622527591%)
2025-08-18 02:05:43.776 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第544/1000条: (113.552521,24.772718) -> (113.582493,24.778732)
2025-08-18 02:05:43.835 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过15.1%)
2025-08-18 02:05:44.135 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第545/1000条: (114.313104,25.121228) -> (114.014315,25.182799)
2025-08-18 02:05:44.213 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.72855755750011%)
2025-08-18 02:05:44.525 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第546/1000条: (113.586702,24.800764) -> (113.558708,24.799452)
2025-08-18 02:05:44.568 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.078758046212604%)
2025-08-18 02:05:44.882 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第547/1000条: (113.278081,24.771981) -> (113.600196,24.788492)
2025-08-18 02:05:44.950 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过36.58%)
2025-08-18 02:05:45.258 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第548/1000条: (113.592008,24.793988) -> (113.564305,24.80017)
2025-08-18 02:05:45.313 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.382295859769318%)
2025-08-18 02:05:45.619 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第549/1000条: (113.356061,25.198235) -> (114.321163,25.117883)
2025-08-18 02:05:45.723 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.76%)
2025-08-18 02:05:46.024 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第550/1000条: (113.607056,24.804331) -> (113.669771,24.608995)
2025-08-18 02:05:46.100 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:46.413 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第551/1000条: (113.600319,24.839798) -> (114.074085,24.946242)
2025-08-18 02:05:46.479 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过45.47%)
2025-08-18 02:05:46.787 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第552/1000条: (113.587183,24.795651) -> (114.137271,24.361447)
2025-08-18 02:05:46.862 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.85%)
2025-08-18 02:05:47.175 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第553/1000条: (114.307626,25.110196) -> (114.310508,25.070864)
2025-08-18 02:05:47.218 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.566099644556889%)
2025-08-18 02:05:47.520 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第554/1000条: (114.346642,25.195819) -> (113.198297,24.856273)
2025-08-18 02:05:47.623 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:47.925 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第555/1000条: (113.58834,24.796571) -> (113.147422,24.987141)
2025-08-18 02:05:48.015 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.43%)
2025-08-18 02:05:48.331 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第556/1000条: (113.180508,25.437686) -> (113.588587,24.818755)
2025-08-18 02:05:48.433 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.76%)
2025-08-18 02:05:48.735 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第557/1000条: (113.603188,24.792137) -> (113.741671,24.774821)
2025-08-18 02:05:48.801 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.05%)
2025-08-18 02:05:49.115 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第558/1000条: (114.268916,25.068273) -> (113.596707,24.824025)
2025-08-18 02:05:49.177 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.96%)
2025-08-18 02:05:49.486 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第559/1000条: (113.286887,24.776535) -> (113.57577,24.793558)
2025-08-18 02:05:49.568 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:49.874 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第560/1000条: (113.634806,24.691875) -> (113.569692,24.798953)
2025-08-18 02:05:49.942 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过21.15%)
2025-08-18 02:05:50.249 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第561/1000条: (113.609684,24.697007) -> (113.600174,24.787999)
2025-08-18 02:05:50.308 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:50.620 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第562/1000条: (113.461961,24.728928) -> (113.404361,24.870651)
2025-08-18 02:05:50.687 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.668577243741982%)
2025-08-18 02:05:50.993 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第563/1000条: (113.468473,25.157958) -> (113.839134,24.368713)
2025-08-18 02:05:51.094 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过99.64%)
2025-08-18 02:05:51.395 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第564/1000条: (114.316388,25.11709) -> (113.805529,24.951753)
2025-08-18 02:05:51.455 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.72%)
2025-08-18 02:05:51.770 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第565/1000条: (113.454917,24.731762) -> (113.577083,24.797087)
2025-08-18 02:05:51.841 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:52.144 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第566/1000条: (113.346369,25.12481) -> (113.352953,25.131954)
2025-08-18 02:05:52.184 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:52.488 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第567/1000条: (114.131782,24.347919) -> (113.827197,24.375637)
2025-08-18 02:05:52.543 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:52.846 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第568/1000条: (113.55134,24.8287) -> (113.594271,24.68996)
2025-08-18 02:05:52.926 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过21.87%)
2025-08-18 02:05:53.236 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第569/1000条: (113.585418,24.752218) -> (113.598839,24.570816)
2025-08-18 02:05:53.306 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过31.102071959357136%)
2025-08-18 02:05:53.613 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第570/1000条: (113.522554,24.672894) -> (113.04757,25.28601)
2025-08-18 02:05:53.685 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.12%)
2025-08-18 02:05:53.987 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第571/1000条: (113.15305,25.378223) -> (114.193255,24.054693)
2025-08-18 02:05:54.114 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:05:54.417 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第572/1000条: (113.583183,24.778097) -> (114.112444,24.004977)
2025-08-18 02:05:54.784 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.57%)
2025-08-18 02:05:55.091 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第573/1000条: (113.284758,24.773964) -> (114.140148,24.355817)
2025-08-18 02:05:55.189 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.86%)
2025-08-18 02:05:55.500 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第574/1000条: (114.302406,25.108696) -> (113.598679,25.258883)
2025-08-18 02:05:55.601 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过100.11%)
2025-08-18 02:05:55.903 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第575/1000条: (113.306177,24.744979) -> (113.608291,24.679423)
2025-08-18 02:05:55.983 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.23%)
2025-08-18 02:05:56.293 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第576/1000条: (113.572042,24.822559) -> (113.427912,24.944279)
2025-08-18 02:05:56.349 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:56.665 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第577/1000条: (113.564305,24.80017) -> (113.568349,24.800264)
2025-08-18 02:05:56.717 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.8412753855209276%)
2025-08-18 02:05:57.024 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第578/1000条: (113.591325,24.644425) -> (113.844071,25.232288)
2025-08-18 02:05:57.121 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:05:57.433 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第579/1000条: (113.182209,25.43937) -> (113.714328,25.082479)
2025-08-18 02:05:57.617 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.92%)
2025-08-18 02:05:57.930 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第580/1000条: (113.595163,24.685285) -> (113.833123,24.459578)
2025-08-18 02:05:58.010 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.98%)
2025-08-18 02:05:58.323 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第581/1000条: (114.068992,24.94151) -> (113.343793,25.133114)
2025-08-18 02:05:58.423 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.07%)
2025-08-18 02:05:58.723 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第582/1000条: (114.309479,25.116115) -> (114.154214,25.025656)
2025-08-18 02:05:58.789 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.992272714228573%)
2025-08-18 02:05:59.100 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第583/1000条: (114.215622,25.049133) -> (113.595139,24.711635)
2025-08-18 02:05:59.168 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.75%)
2025-08-18 02:05:59.473 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第584/1000条: (113.593281,24.784425) -> (113.830642,25.056242)
2025-08-18 02:05:59.941 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.57%)
2025-08-18 02:06:00.256 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第585/1000条: (113.583183,24.778097) -> (113.230865,24.622416)
2025-08-18 02:06:00.334 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:00.644 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第586/1000条: (113.611589,24.796557) -> (113.071999,25.180093)
2025-08-18 02:06:00.740 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.74%)
2025-08-18 02:06:01.048 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第587/1000条: (113.355101,25.116801) -> (113.749118,25.089091)
2025-08-18 02:06:01.124 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.13%)
2025-08-18 02:06:01.433 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第588/1000条: (113.269044,24.778633) -> (113.350685,25.132413)
2025-08-18 02:06:01.525 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:01.839 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第589/1000条: (114.320975,24.121624) -> (114.202214,24.053731)
2025-08-18 02:06:01.890 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.78793403374025%)
2025-08-18 02:06:02.197 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第590/1000条: (113.598486,24.787637) -> (114.307037,25.117926)
2025-08-18 02:06:02.270 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.45%)
2025-08-18 02:06:02.583 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第591/1000条: (113.561426,24.843879) -> (114.133461,24.344647)
2025-08-18 02:06:02.681 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过89.32%)
2025-08-18 02:06:02.991 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第592/1000条: (113.641896,24.638732) -> (114.049258,24.954539)
2025-08-18 02:06:03.066 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.93%)
2025-08-18 02:06:03.370 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第593/1000条: (114.384034,25.24571) -> (113.858383,24.267485)
2025-08-18 02:06:03.495 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过114.54%)
2025-08-18 02:06:03.808 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第594/1000条: (114.305909,25.110592) -> (114.085025,24.687204)
2025-08-18 02:06:03.996 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.60853568187358%)
2025-08-18 02:06:04.308 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第595/1000条: (113.538386,24.754911) -> (114.088929,24.923268)
2025-08-18 02:06:04.434 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.43%)
2025-08-18 02:06:04.740 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第596/1000条: (114.716155,25.241483) -> (113.522061,24.748939)
2025-08-18 02:06:04.845 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过115.13%)
2025-08-18 02:06:05.150 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第597/1000条: (113.366982,25.111671) -> (114.063307,24.947049)
2025-08-18 02:06:05.268 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.99%)
2025-08-18 02:06:05.570 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第598/1000条: (114.151281,25.236341) -> (113.258284,25.455503)
2025-08-18 02:06:05.669 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:05.974 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第599/1000条: (113.56282,24.829124) -> (113.592008,24.793988)
2025-08-18 02:06:06.025 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.02%)
2025-08-18 02:06:06.332 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第600/1000条: (114.395197,25.168799) -> (113.588543,24.793039)
2025-08-18 02:06:06.423 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.19%)
2025-08-18 02:06:06.724 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第601/1000条: (113.430339,24.940683) -> (113.290761,24.772893)
2025-08-18 02:06:06.775 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过37.117324167449326%)
2025-08-18 02:06:07.083 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第602/1000条: (113.394954,25.078365) -> (113.743802,25.083982)
2025-08-18 02:06:07.175 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.04%)
2025-08-18 02:06:07.488 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第603/1000条: (113.576893,24.819747) -> (113.602522,24.522024)
2025-08-18 02:06:07.565 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.82%)
2025-08-18 02:06:07.880 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第604/1000条: (113.674803,24.605205) -> (113.588555,24.816102)
2025-08-18 02:06:07.940 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.57%)
2025-08-18 02:06:08.252 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第605/1000条: (113.636468,24.664952) -> (113.600196,24.788492)
2025-08-18 02:06:08.304 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.15%)
2025-08-18 02:06:08.608 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第606/1000条: (113.404361,24.870651) -> (114.077887,24.954748)
2025-08-18 02:06:08.708 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.04%)
2025-08-18 02:06:09.016 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第607/1000条: (113.602834,24.812793) -> (114.135725,24.523023)
2025-08-18 02:06:09.114 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.61%)
2025-08-18 02:06:09.421 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第608/1000条: (113.142074,24.539945) -> (113.618841,25.09516)
2025-08-18 02:06:09.517 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过116.96%)
2025-08-18 02:06:09.825 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第609/1000条: (113.392144,25.074625) -> (113.92477,24.153127)
2025-08-18 02:06:10.931 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.38%)
2025-08-18 02:06:11.236 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第610/1000条: (113.609207,24.680708) -> (113.611426,24.847112)
2025-08-18 02:06:12.366 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.6%)
2025-08-18 02:06:12.671 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第611/1000条: (113.541131,24.800106) -> (114.303818,25.116538)
2025-08-18 02:06:14.564 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.32%)
2025-08-18 02:06:14.874 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第612/1000条: (113.533624,24.891498) -> (114.039173,24.18568)
2025-08-18 02:06:18.790 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过110.09%)
2025-08-18 02:06:19.099 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第613/1000条: (113.544043,24.763413) -> (113.596481,24.785717)
2025-08-18 02:06:19.434 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:19.738 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第614/1000条: (113.60763,24.828038) -> (114.316388,25.11709)
2025-08-18 02:06:19.911 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.98%)
2025-08-18 02:06:20.224 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第615/1000条: (114.081799,24.950213) -> (114.132225,24.352394)
2025-08-18 02:06:20.666 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过60.47%)
2025-08-18 02:06:20.970 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第616/1000条: (113.474268,25.161452) -> (114.542513,25.244804)
2025-08-18 02:06:22.151 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:22.464 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第617/1000条: (113.351508,25.128528) -> (113.622403,24.671573)
2025-08-18 02:06:22.812 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过62.05%)
2025-08-18 02:06:23.120 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第618/1000条: (113.420654,24.93126) -> (113.426684,25.169685)
2025-08-18 02:06:23.173 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:23.478 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第619/1000条: (113.347218,25.391851) -> (113.943643,25.133792)
2025-08-18 02:06:23.580 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过99.73109089740784%)
2025-08-18 02:06:23.881 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第620/1000条: (113.358329,25.037371) -> (113.167884,25.435189)
2025-08-18 02:06:23.960 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.37%)
2025-08-18 02:06:24.270 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第621/1000条: (113.564574,24.799385) -> (113.571284,24.798596)
2025-08-18 02:06:24.324 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:24.629 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第622/1000条: (113.982329,24.608784) -> (113.578678,24.723723)
2025-08-18 02:06:24.719 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.24%)
2025-08-18 02:06:25.033 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第623/1000条: (113.601189,24.789757) -> (113.278651,24.769408)
2025-08-18 02:06:25.110 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过37.16%)
2025-08-18 02:06:25.421 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第624/1000条: (113.598257,24.526049) -> (113.58238,24.775981)
2025-08-18 02:06:25.473 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.95%)
2025-08-18 02:06:25.780 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第625/1000条: (114.438337,25.084592) -> (113.564574,24.799385)
2025-08-18 02:06:25.857 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过92.52%)
2025-08-18 02:06:26.172 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第626/1000条: (113.60763,24.828038) -> (113.934611,24.978389)
2025-08-18 02:06:26.231 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.12%)
2025-08-18 02:06:26.531 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第627/1000条: (113.521913,24.788187) -> (113.604889,24.81119)
2025-08-18 02:06:26.583 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.77%)
2025-08-18 02:06:26.891 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第628/1000条: (113.605666,24.790311) -> (113.589291,24.767901)
2025-08-18 02:06:26.951 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.872848593472043%)
2025-08-18 02:06:27.266 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第629/1000条: (114.479767,25.152038) -> (113.576249,24.80108)
2025-08-18 02:06:27.364 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.54%)
2025-08-18 02:06:27.670 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第630/1000条: (113.293668,24.772292) -> (114.150068,25.027748)
2025-08-18 02:06:27.764 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.26%)
2025-08-18 02:06:28.074 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第631/1000条: (113.746437,24.831225) -> (113.534303,24.784778)
2025-08-18 02:06:28.147 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.1%)
2025-08-18 02:06:28.461 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第632/1000条: (114.318552,25.112633) -> (114.692249,25.281171)
2025-08-18 02:06:28.519 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:28.834 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第633/1000条: (113.596707,24.824025) -> (113.581583,25.076872)
2025-08-18 02:06:28.911 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:29.223 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第634/1000条: (113.536028,24.897287) -> (114.479169,25.148869)
2025-08-18 02:06:29.321 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过96.84%)
2025-08-18 02:06:29.632 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第635/1000条: (114.026744,25.188164) -> (113.586163,24.805247)
2025-08-18 02:06:29.730 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:30.035 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第636/1000条: (113.358375,25.159845) -> (113.394774,25.077713)
2025-08-18 02:06:30.096 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.020215765847077%)
2025-08-18 02:06:30.407 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第637/1000条: (113.589291,24.767901) -> (113.288146,24.780403)
2025-08-18 02:06:30.467 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过31.05%)
2025-08-18 02:06:30.781 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第638/1000条: (114.064067,24.948659) -> (114.197445,24.048847)
2025-08-18 02:06:30.892 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.2%)
2025-08-18 02:06:31.200 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第639/1000条: (114.151394,25.027188) -> (113.11442,25.238855)
2025-08-18 02:06:31.304 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.99%)
2025-08-18 02:06:31.604 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第640/1000条: (113.646255,24.786377) -> (113.598245,24.788065)
2025-08-18 02:06:31.655 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过10.32812669194038%)
2025-08-18 02:06:31.961 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第641/1000条: (114.024063,25.18527) -> (113.533729,24.753516)
2025-08-18 02:06:32.047 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:32.353 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第642/1000条: (113.788491,24.783949) -> (113.572042,24.822559)
2025-08-18 02:06:32.419 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.71%)
2025-08-18 02:06:32.726 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第643/1000条: (113.504393,24.91505) -> (113.14235,24.917176)
2025-08-18 02:06:32.790 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.85%)
2025-08-18 02:06:33.102 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第644/1000条: (114.093378,24.732606) -> (114.202214,24.053731)
2025-08-18 02:06:33.194 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.45%)
2025-08-18 02:06:33.507 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第645/1000条: (113.30287,24.757609) -> (113.626471,24.770683)
2025-08-18 02:06:33.582 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过36.13%)
2025-08-18 02:06:33.897 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第646/1000条: (113.628627,24.791036) -> (113.622403,24.671573)
2025-08-18 02:06:33.961 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.97%)
2025-08-18 02:06:34.268 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第647/1000条: (116.59269,24.808367) -> (113.552856,24.800859)
2025-08-18 02:06:34.438 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:34.750 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第648/1000条: (114.088929,24.923268) -> (113.272826,24.772738)
2025-08-18 02:06:34.848 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过78.35%)
2025-08-18 02:06:35.158 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第649/1000条: (114.479169,25.148869) -> (114.115085,24.001211)
2025-08-18 02:06:35.267 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:35.578 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第650/1000条: (113.345161,24.515271) -> (113.595824,24.804988)
2025-08-18 02:06:35.649 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过61.99%)
2025-08-18 02:06:35.951 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第651/1000条: (114.313928,25.121581) -> (114.298966,25.114249)
2025-08-18 02:06:36.003 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:36.312 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第652/1000条: (113.045956,25.283825) -> (114.495151,25.225136)
2025-08-18 02:06:36.436 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:36.750 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第653/1000条: (113.389862,25.131521) -> (114.306562,25.110817)
2025-08-18 02:06:36.846 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.49%)
2025-08-18 02:06:37.157 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第654/1000条: (113.377637,25.340258) -> (114.131459,24.358299)
2025-08-18 02:06:37.264 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:37.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第655/1000条: (113.570066,25.001682) -> (113.411468,25.128546)
2025-08-18 02:06:37.656 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过44.63%)
2025-08-18 02:06:37.965 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第656/1000条: (114.520165,25.077396) -> (114.199332,24.046063)
2025-08-18 02:06:38.092 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:38.400 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第657/1000条: (113.532205,24.757414) -> (114.321526,25.122851)
2025-08-18 02:06:38.482 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.56%)
2025-08-18 02:06:38.786 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第658/1000条: (113.543727,24.766491) -> (113.57543,24.823916)
2025-08-18 02:06:38.834 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.860968587877371%)
2025-08-18 02:06:39.146 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第659/1000条: (113.589946,24.809256) -> (114.067355,24.946552)
2025-08-18 02:06:39.227 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.7%)
2025-08-18 02:06:39.534 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第660/1000条: (113.674803,24.605205) -> (114.426929,25.0811)
2025-08-18 02:06:39.630 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过103.89%)
2025-08-18 02:06:39.941 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第661/1000条: (113.679272,24.774007) -> (113.741671,24.774821)
2025-08-18 02:06:39.982 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.15%)
2025-08-18 02:06:40.287 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第662/1000条: (114.298621,25.134346) -> (113.749118,25.089091)
2025-08-18 02:06:40.372 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.09%)
2025-08-18 02:06:40.676 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第663/1000条: (114.393826,25.170427) -> (113.561667,24.780641)
2025-08-18 02:06:40.758 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过87.12%)
2025-08-18 02:06:41.064 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第664/1000条: (113.35124,25.144127) -> (113.334273,25.134187)
2025-08-18 02:06:41.119 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.450901917711371%)
2025-08-18 02:06:41.422 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第665/1000条: (114.294455,25.113379) -> (113.61291,24.851447)
2025-08-18 02:06:41.502 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.57%)
2025-08-18 02:06:41.811 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第666/1000条: (113.043211,25.28791) -> (113.598245,24.788065)
2025-08-18 02:06:41.912 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.25%)
2025-08-18 02:06:42.218 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第667/1000条: (113.586705,24.787225) -> (113.993103,24.926555)
2025-08-18 02:06:42.283 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.55%)
2025-08-18 02:06:42.591 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第668/1000条: (113.59917,24.788866) -> (113.597767,24.785922)
2025-08-18 02:06:42.633 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.97%)
2025-08-18 02:06:42.948 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第669/1000条: (114.151281,25.236341) -> (114.631328,25.218075)
2025-08-18 02:06:43.046 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:43.353 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第670/1000条: (113.56216,24.838735) -> (113.982329,24.608784)
2025-08-18 02:06:43.449 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.49%)
2025-08-18 02:06:43.759 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第671/1000条: (113.61898,24.693889) -> (114.197967,24.046949)
2025-08-18 02:06:43.864 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过110.33%)
2025-08-18 02:06:44.178 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第672/1000条: (113.587072,24.758847) -> (113.374479,25.139548)
2025-08-18 02:06:44.268 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.74%)
2025-08-18 02:06:44.581 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第673/1000条: (113.582764,24.777482) -> (114.308512,25.119122)
2025-08-18 02:06:44.651 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.49%)
2025-08-18 02:06:44.954 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第674/1000条: (113.591129,24.827811) -> (114.315055,25.121929)
2025-08-18 02:06:45.133 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.49%)
2025-08-18 02:06:45.438 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第675/1000条: (114.358719,25.189944) -> (113.926255,23.996698)
2025-08-18 02:06:45.549 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:45.857 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第676/1000条: (114.305296,25.111844) -> (113.528143,24.78718)
2025-08-18 02:06:45.961 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.67%)
2025-08-18 02:06:46.262 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第677/1000条: (113.761405,24.777583) -> (113.583417,24.797819)
2025-08-18 02:06:46.332 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.19%)
2025-08-18 02:06:46.635 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第678/1000条: (113.061182,25.280423) -> (113.274721,24.782942)
2025-08-18 02:06:46.696 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.46%)
2025-08-18 02:06:47.011 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第679/1000条: (113.470878,25.151371) -> (113.993103,24.926555)
2025-08-18 02:06:47.102 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.84%)
2025-08-18 02:06:47.416 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第680/1000条: (113.591937,24.775475) -> (113.587449,24.794473)
2025-08-18 02:06:47.473 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.67%)
2025-08-18 02:06:47.776 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第681/1000条: (113.454639,25.15278) -> (113.595163,24.685285)
2025-08-18 02:06:47.863 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.88%)
2025-08-18 02:06:48.163 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第682/1000条: (113.581993,24.742679) -> (114.127122,24.437165)
2025-08-18 02:06:48.240 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.16%)
2025-08-18 02:06:48.553 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第683/1000条: (113.456023,24.727554) -> (113.570921,24.718796)
2025-08-18 02:06:48.609 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.82%)
2025-08-18 02:06:48.925 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第684/1000条: (113.585202,24.754122) -> (113.749118,25.089091)
2025-08-18 02:06:49.012 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.69069800467146%)
2025-08-18 02:06:49.328 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第685/1000条: (113.588044,24.804015) -> (113.461961,24.728928)
2025-08-18 02:06:49.393 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.093393242847657%)
2025-08-18 02:06:49.705 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第686/1000条: (113.631865,24.817671) -> (113.572681,24.7967)
2025-08-18 02:06:49.774 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.2%)
2025-08-18 02:06:50.079 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第687/1000条: (113.60763,24.828038) -> (113.610525,24.810531)
2025-08-18 02:06:50.131 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.219321647504469%)
2025-08-18 02:06:50.438 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第688/1000条: (113.577252,24.757727) -> (113.573997,24.793932)
2025-08-18 02:06:50.497 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.69%)
2025-08-18 02:06:50.798 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第689/1000条: (113.334596,25.427961) -> (113.480285,24.739769)
2025-08-18 02:06:50.884 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.91%)
2025-08-18 02:06:51.191 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第690/1000条: (113.579968,24.797236) -> (113.640082,25.110964)
2025-08-18 02:06:51.263 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:51.565 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第691/1000条: (113.581009,24.801379) -> (113.276527,24.777478)
2025-08-18 02:06:51.624 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:06:51.934 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第692/1000条: (114.497229,25.236103) -> (113.530886,24.847659)
2025-08-18 02:06:52.050 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.33%)
2025-08-18 02:06:52.354 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第693/1000条: (113.58442,24.778843) -> (113.583034,24.784894)
2025-08-18 02:06:52.408 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.948680253119936%)
2025-08-18 02:06:52.713 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第694/1000条: (113.532832,24.751522) -> (114.310059,25.116844)
2025-08-18 02:06:52.796 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.02%)
2025-08-18 02:06:53.100 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第695/1000条: (113.231143,24.6236) -> (113.613883,24.680026)
2025-08-18 02:06:53.174 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.03%)
2025-08-18 02:06:53.476 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第696/1000条: (114.291377,25.113797) -> (113.824689,24.966239)
2025-08-18 02:06:53.554 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.16%)
2025-08-18 02:06:53.862 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第697/1000条: (113.144564,24.538) -> (113.021415,25.30728)
2025-08-18 02:06:53.938 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过104.18%)
2025-08-18 02:06:54.253 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第698/1000条: (113.547556,24.791917) -> (113.640664,24.825489)
2025-08-18 02:06:54.306 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.46%)
2025-08-18 02:06:54.608 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第699/1000条: (113.352789,25.126153) -> (113.582493,24.778732)
2025-08-18 02:06:54.689 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.93%)
2025-08-18 02:06:54.994 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第700/1000条: (113.473357,25.145196) -> (114.067458,24.474961)
2025-08-18 02:06:55.147 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.51%)
2025-08-18 02:06:55.461 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第701/1000条: (113.334273,25.134187) -> (114.346642,25.195819)
2025-08-18 02:06:55.536 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过108.95%)
2025-08-18 02:06:55.850 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第702/1000条: (113.609056,24.685531) -> (114.208235,24.057433)
2025-08-18 02:06:55.951 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.74%)
2025-08-18 02:06:56.255 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第703/1000条: (113.586858,24.817181) -> (113.972193,24.63009)
2025-08-18 02:06:56.327 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.09%)
2025-08-18 02:06:56.628 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第704/1000条: (114.60175,25.260704) -> (114.304841,25.106569)
2025-08-18 02:06:56.706 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过52.33683568145567%)
2025-08-18 02:06:57.020 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第705/1000条: (113.343793,25.133114) -> (113.74233,25.085314)
2025-08-18 02:06:57.103 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.28%)
2025-08-18 02:06:57.406 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第706/1000条: (113.626033,24.692593) -> (114.21582,24.060932)
2025-08-18 02:06:57.505 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过111.49%)
2025-08-18 02:06:57.810 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第707/1000条: (113.287563,24.778928) -> (114.196598,25.125256)
2025-08-18 02:06:57.915 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.25%)
2025-08-18 02:06:58.230 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第708/1000条: (114.456557,25.258442) -> (113.388156,25.014333)
2025-08-18 02:06:58.312 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:06:58.620 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第709/1000条: (114.289174,25.106499) -> (113.582764,24.777482)
2025-08-18 02:06:58.686 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.56%)
2025-08-18 02:06:58.993 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第710/1000条: (113.52998,24.767209) -> (113.533729,24.753516)
2025-08-18 02:06:59.032 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.06967583903361%)
2025-08-18 02:06:59.333 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第711/1000条: (113.538858,24.674736) -> (113.698642,24.767988)
2025-08-18 02:06:59.396 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过21.83%)
2025-08-18 02:06:59.708 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第712/1000条: (113.745144,25.089252) -> (113.345067,24.517248)
2025-08-18 02:06:59.805 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:00.113 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第713/1000条: (113.598486,24.787637) -> (113.596128,24.811885)
2025-08-18 02:07:00.165 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:00.471 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第714/1000条: (113.801394,24.044648) -> (114.214888,24.060932)
2025-08-18 02:07:00.545 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过65.0809573091773%)
2025-08-18 02:07:00.846 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第715/1000条: (113.970582,24.307071) -> (114.11656,24.354095)
2025-08-18 02:07:00.921 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.53587178306496%)
2025-08-18 02:07:01.235 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第716/1000条: (113.598245,24.788065) -> (113.646255,24.786377)
2025-08-18 02:07:01.278 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.262005256154602%)
2025-08-18 02:07:01.593 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第717/1000条: (113.461961,24.728928) -> (113.578992,24.798214)
2025-08-18 02:07:01.643 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:01.951 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第718/1000条: (113.539613,24.757645) -> (113.534303,24.784778)
2025-08-18 02:07:02.006 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:02.310 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第719/1000条: (114.140836,24.915321) -> (114.211297,24.860204)
2025-08-18 02:07:02.361 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过37.78%)
2025-08-18 02:07:02.669 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第720/1000条: (113.35124,25.144127) -> (113.582163,24.774319)
2025-08-18 02:07:02.748 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.35%)
2025-08-18 02:07:03.061 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第721/1000条: (114.353523,25.285992) -> (114.473623,25.116658)
2025-08-18 02:07:03.128 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过35.06042138009216%)
2025-08-18 02:07:03.436 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第722/1000条: (114.492142,25.224319) -> (114.223273,24.053476)
2025-08-18 02:07:03.555 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:03.858 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第723/1000条: (113.285764,24.780136) -> (114.384034,25.24571)
2025-08-18 02:07:03.968 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.36%)
2025-08-18 02:07:04.280 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第724/1000条: (113.598252,24.674241) -> (113.943643,25.133792)
2025-08-18 02:07:04.387 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.18034915706761%)
2025-08-18 02:07:04.699 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第725/1000条: (114.599893,25.260236) -> (113.865906,25.201329)
2025-08-18 02:07:04.778 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过108.41%)
2025-08-18 02:07:05.087 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第726/1000条: (113.57577,24.793558) -> (113.491088,24.747843)
2025-08-18 02:07:05.135 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.26%)
2025-08-18 02:07:05.444 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第727/1000条: (114.323738,25.126666) -> (114.064974,24.950458)
2025-08-18 02:07:05.514 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.68%)
2025-08-18 02:07:05.819 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第728/1000条: (113.285455,24.76836) -> (114.071988,24.94872)
2025-08-18 02:07:05.907 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.22%)
2025-08-18 02:07:06.209 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第729/1000条: (113.387391,24.831489) -> (113.129062,25.169007)
2025-08-18 02:07:06.295 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.72%)
2025-08-18 02:07:06.600 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第730/1000条: (113.628627,24.791036) -> (114.078911,24.94844)
2025-08-18 02:07:06.683 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.46%)
2025-08-18 02:07:06.987 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第731/1000条: (114.297897,25.121221) -> (114.065141,24.948065)
2025-08-18 02:07:07.048 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.29%)
2025-08-18 02:07:07.361 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第732/1000条: (113.533513,24.786953) -> (113.547556,24.791917)
2025-08-18 02:07:07.413 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.171704988211664%)
2025-08-18 02:07:07.722 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第733/1000条: (113.584738,24.793041) -> (113.143829,24.538413)
2025-08-18 02:07:07.797 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.90004883359181%)
2025-08-18 02:07:08.109 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第734/1000条: (113.367635,25.12753) -> (113.367031,25.134895)
2025-08-18 02:07:08.160 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.05799221878363%)
2025-08-18 02:07:08.469 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第735/1000条: (114.313822,25.119112) -> (113.569692,24.798953)
2025-08-18 02:07:08.566 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.39%)
2025-08-18 02:07:08.875 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第736/1000条: (113.521458,24.675167) -> (113.601189,24.789757)
2025-08-18 02:07:08.930 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.65%)
2025-08-18 02:07:09.230 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第737/1000条: (113.598938,24.678294) -> (114.17876,24.028903)
2025-08-18 02:07:09.334 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过111.33%)
2025-08-18 02:07:09.649 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第738/1000条: (113.310826,25.013573) -> (113.583117,24.788585)
2025-08-18 02:07:09.710 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:10.025 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第739/1000条: (113.588543,24.793039) -> (113.580213,24.800478)
2025-08-18 02:07:10.082 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.172512106379735%)
2025-08-18 02:07:10.386 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第740/1000条: (114.245011,25.259782) -> (113.420654,24.93126)
2025-08-18 02:07:10.499 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过105.78%)
2025-08-18 02:07:10.808 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第741/1000条: (113.027563,24.565083) -> (113.60229,24.81692)
2025-08-18 02:07:10.885 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.51%)
2025-08-18 02:07:11.198 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第742/1000条: (113.356817,25.123887) -> (114.065794,24.942697)
2025-08-18 02:07:11.277 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.89%)
2025-08-18 02:07:11.587 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第743/1000条: (113.293688,24.775343) -> (113.569352,24.80258)
2025-08-18 02:07:11.645 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.7%)
2025-08-18 02:07:11.961 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第744/1000条: (113.524565,24.787409) -> (113.825647,24.707275)
2025-08-18 02:07:12.019 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.25%)
2025-08-18 02:07:12.333 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第745/1000条: (114.302097,25.116387) -> (113.582163,24.774319)
2025-08-18 02:07:12.430 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.84%)
2025-08-18 02:07:12.740 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第746/1000条: (113.128626,24.521906) -> (114.116314,24.004907)
2025-08-18 02:07:12.869 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:13.176 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第747/1000条: (113.601189,24.789757) -> (113.460609,24.979835)
2025-08-18 02:07:13.234 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:13.549 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第748/1000条: (113.539613,24.757645) -> (113.60418,24.675561)
2025-08-18 02:07:13.617 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.71%)
2025-08-18 02:07:13.924 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第749/1000条: (114.547949,25.284278) -> (114.199443,24.054239)
2025-08-18 02:07:14.318 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:14.622 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第750/1000条: (113.605291,24.841961) -> (114.393826,25.170427)
2025-08-18 02:07:14.699 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.38%)
2025-08-18 02:07:15.008 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第751/1000条: (113.491088,24.747843) -> (114.142994,24.331111)
2025-08-18 02:07:15.105 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.27%)
2025-08-18 02:07:15.412 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第752/1000条: (113.599146,24.774166) -> (113.60783,24.684552)
2025-08-18 02:07:15.457 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.2388096884089%)
2025-08-18 02:07:15.772 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第753/1000条: (114.713758,25.294158) -> (114.037085,24.245208)
2025-08-18 02:07:15.873 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:16.177 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第754/1000条: (114.315609,25.113869) -> (114.280666,24.76335)
2025-08-18 02:07:16.263 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.73%)
2025-08-18 02:07:16.564 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第755/1000条: (113.619938,24.686659) -> (114.303569,25.123947)
2025-08-18 02:07:16.655 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.63%)
2025-08-18 02:07:16.970 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第756/1000条: (113.027563,24.565083) -> (113.561667,24.780641)
2025-08-18 02:07:17.043 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:17.344 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第757/1000条: (113.56282,24.829124) -> (113.591437,24.686242)
2025-08-18 02:07:17.415 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.548212458157632%)
2025-08-18 02:07:17.719 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第758/1000条: (113.58507,24.763468) -> (113.600182,24.67531)
2025-08-18 02:07:17.784 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:18.089 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第759/1000条: (114.421616,25.14868) -> (114.087682,24.94275)
2025-08-18 02:07:18.165 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.69%)
2025-08-18 02:07:18.478 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第760/1000条: (113.599248,24.799582) -> (114.125819,24.359885)
2025-08-18 02:07:18.593 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.39%)
2025-08-18 02:07:18.901 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第761/1000条: (113.531246,24.756334) -> (114.096828,24.190059)
2025-08-18 02:07:19.393 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过114.7%)
2025-08-18 02:07:19.694 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第762/1000条: (114.291887,25.10602) -> (114.306859,25.116767)
2025-08-18 02:07:19.740 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.413541300099144%)
2025-08-18 02:07:20.051 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第763/1000条: (113.564343,24.804194) -> (113.434783,24.946099)
2025-08-18 02:07:20.099 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:20.411 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第764/1000条: (113.554589,24.829621) -> (113.358329,25.037371)
2025-08-18 02:07:20.475 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:20.788 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第765/1000条: (113.274721,24.782942) -> (113.586501,24.784123)
2025-08-18 02:07:20.866 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:21.176 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第766/1000条: (113.292907,24.774933) -> (113.491088,24.747843)
2025-08-18 02:07:21.248 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.58487108911228%)
2025-08-18 02:07:21.549 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第767/1000条: (113.532154,24.893079) -> (113.565042,24.849047)
2025-08-18 02:07:21.590 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:21.892 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第768/1000条: (114.214035,24.860307) -> (113.601225,24.677987)
2025-08-18 02:07:21.986 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.41%)
2025-08-18 02:07:22.300 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第769/1000条: (113.605337,24.682306) -> (113.272747,24.777425)
2025-08-18 02:07:22.369 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过34.38%)
2025-08-18 02:07:22.673 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第770/1000条: (113.357319,25.127257) -> (114.069694,24.946767)
2025-08-18 02:07:22.773 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.14%)
2025-08-18 02:07:23.080 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第771/1000条: (114.081799,24.950213) -> (113.598449,24.800212)
2025-08-18 02:07:23.162 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.89%)
2025-08-18 02:07:23.465 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第772/1000条: (114.353523,25.285992) -> (113.597629,24.814954)
2025-08-18 02:07:23.541 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过79.21%)
2025-08-18 02:07:23.854 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第773/1000条: (113.356061,25.198235) -> (113.577958,24.795753)
2025-08-18 02:07:23.929 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.67%)
2025-08-18 02:07:24.231 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第774/1000条: (114.309715,25.118263) -> (114.085025,24.687204)
2025-08-18 02:07:24.315 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.64%)
2025-08-18 02:07:24.619 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第775/1000条: (114.251469,25.149927) -> (113.463317,24.879027)
2025-08-18 02:07:24.714 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.49%)
2025-08-18 02:07:25.025 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第776/1000条: (113.583458,24.820571) -> (113.326633,24.806995)
2025-08-18 02:07:25.094 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.95%)
2025-08-18 02:07:25.396 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第777/1000条: (114.063307,24.947049) -> (113.52577,24.785277)
2025-08-18 02:07:25.465 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过53.78%)
2025-08-18 02:07:25.770 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第778/1000条: (113.072692,25.181274) -> (113.753251,25.080194)
2025-08-18 02:07:25.842 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.18%)
2025-08-18 02:07:26.142 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第779/1000条: (113.056804,25.283912) -> (113.073138,25.174875)
2025-08-18 02:07:26.208 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:26.512 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第780/1000条: (114.310508,25.070864) -> (114.296506,25.107913)
2025-08-18 02:07:26.564 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:26.869 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第781/1000条: (113.299272,24.757754) -> (113.432972,24.94721)
2025-08-18 02:07:26.944 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:27.258 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第782/1000条: (113.582902,24.736381) -> (113.627145,24.791868)
2025-08-18 02:07:27.329 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.30972506314019%)
2025-08-18 02:07:27.633 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第783/1000条: (113.585202,24.754122) -> (114.307037,25.117926)
2025-08-18 02:07:27.731 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.87%)
2025-08-18 02:07:28.040 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第784/1000条: (113.430339,24.940683) -> (113.575352,24.806232)
2025-08-18 02:07:28.097 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.29%)
2025-08-18 02:07:28.411 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第785/1000条: (113.590988,24.77208) -> (114.132963,24.359787)
2025-08-18 02:07:28.514 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过82.97%)
2025-08-18 02:07:28.817 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第786/1000条: (113.53297,24.758084) -> (113.58724,24.789703)
2025-08-18 02:07:28.869 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.634372376875012%)
2025-08-18 02:07:29.177 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第787/1000条: (114.310006,25.112769) -> (114.297897,25.121221)
2025-08-18 02:07:29.229 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.072541977690296%)
2025-08-18 02:07:29.532 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第788/1000条: (113.608731,24.794747) -> (113.618841,25.09516)
2025-08-18 02:07:29.611 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:29.920 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第789/1000条: (113.289765,24.777538) -> (114.059076,24.26186)
2025-08-18 02:07:29.999 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过98.31%)
2025-08-18 02:07:30.309 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第790/1000条: (113.322701,24.739501) -> (113.019082,25.300336)
2025-08-18 02:07:30.400 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.0%)
2025-08-18 02:07:30.713 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第791/1000条: (113.461961,24.728928) -> (113.827094,24.968845)
2025-08-18 02:07:30.792 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过39.38%)
2025-08-18 02:07:31.101 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第792/1000条: (113.674734,24.611325) -> (113.595023,24.673454)
2025-08-18 02:07:31.167 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.91%)
2025-08-18 02:07:31.474 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第793/1000条: (113.703404,24.766431) -> (113.556485,24.779326)
2025-08-18 02:07:31.548 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.34%)
2025-08-18 02:07:31.849 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第794/1000条: (114.304841,25.106569) -> (113.354915,25.120446)
2025-08-18 02:07:31.929 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过98.5%)
2025-08-18 02:07:32.239 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第795/1000条: (113.590462,24.774129) -> (114.315055,25.121929)
2025-08-18 02:07:32.335 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.97%)
2025-08-18 02:07:32.641 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第796/1000条: (113.534855,24.890005) -> (113.587462,24.767926)
2025-08-18 02:07:32.692 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过25.55791995041006%)
2025-08-18 02:07:32.997 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第797/1000条: (114.407235,25.175169) -> (113.345067,24.517248)
2025-08-18 02:07:33.102 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:33.415 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第798/1000条: (113.561426,24.843879) -> (113.744233,25.042364)
2025-08-18 02:07:33.503 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过38.06%)
2025-08-18 02:07:33.807 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第799/1000条: (113.288218,24.779164) -> (113.581009,24.801379)
2025-08-18 02:07:33.883 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.88%)
2025-08-18 02:07:34.198 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第800/1000条: (113.746279,25.088949) -> (113.650047,25.080055)
2025-08-18 02:07:34.252 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.27833128161588%)
2025-08-18 02:07:34.566 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第801/1000条: (114.215622,25.049133) -> (113.598938,24.678294)
2025-08-18 02:07:34.651 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.54%)
2025-08-18 02:07:34.952 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第802/1000条: (113.579177,24.793081) -> (113.604889,24.81119)
2025-08-18 02:07:35.016 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.45%)
2025-08-18 02:07:35.327 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第803/1000条: (113.565536,24.797884) -> (113.271476,24.779885)
2025-08-18 02:07:35.402 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.83%)
2025-08-18 02:07:35.707 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第804/1000条: (114.360197,25.192398) -> (113.587449,24.794473)
2025-08-18 02:07:35.797 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过77.18%)
2025-08-18 02:07:36.112 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第805/1000条: (113.738933,24.71623) -> (114.058277,24.994152)
2025-08-18 02:07:36.173 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.23%)
2025-08-18 02:07:36.487 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第806/1000条: (113.598486,24.787637) -> (113.345161,24.515271)
2025-08-18 02:07:36.567 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过61.344084384825194%)
2025-08-18 02:07:36.877 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第807/1000条: (113.569352,24.80258) -> (114.131501,24.348938)
2025-08-18 02:07:36.970 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.07%)
2025-08-18 02:07:37.282 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第808/1000条: (113.569352,24.80258) -> (113.592008,24.793988)
2025-08-18 02:07:37.333 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:37.639 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第809/1000条: (113.592686,24.6663) -> (113.573582,24.800762)
2025-08-18 02:07:37.707 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过18.67%)
2025-08-18 02:07:38.007 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第810/1000条: (114.214035,24.860307) -> (114.364493,25.193861)
2025-08-18 02:07:38.085 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.35%)
2025-08-18 02:07:38.394 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第811/1000条: (113.547556,24.791917) -> (114.129066,24.327199)
2025-08-18 02:07:38.476 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过92.45%)
2025-08-18 02:07:38.783 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第812/1000条: (113.669982,24.608317) -> (113.595841,24.539097)
2025-08-18 02:07:38.846 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过15.19%)
2025-08-18 02:07:39.154 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第813/1000条: (113.598147,24.787514) -> (113.024308,25.300392)
2025-08-18 02:07:39.254 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.4%)
2025-08-18 02:07:39.558 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第814/1000条: (113.666729,24.856092) -> (113.598147,24.787514)
2025-08-18 02:07:39.605 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.98%)
2025-08-18 02:07:39.919 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第815/1000条: (113.819424,24.788007) -> (113.632645,25.079725)
2025-08-18 02:07:39.989 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过55.04%)
2025-08-18 02:07:40.294 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第816/1000条: (113.588543,24.793039) -> (113.598252,24.674241)
2025-08-18 02:07:40.362 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过16.26%)
2025-08-18 02:07:40.666 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第817/1000条: (113.288218,24.779164) -> (113.410795,25.111289)
2025-08-18 02:07:40.729 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.0%)
2025-08-18 02:07:41.037 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第818/1000条: (114.315055,25.121929) -> (113.597215,24.81204)
2025-08-18 02:07:41.125 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.58%)
2025-08-18 02:07:41.439 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第819/1000条: (113.595023,24.673454) -> (113.532905,24.545201)
2025-08-18 02:07:41.495 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过21.48%)
2025-08-18 02:07:41.799 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第820/1000条: (113.605291,24.841961) -> (114.289258,25.098861)
2025-08-18 02:07:41.888 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.16%)
2025-08-18 02:07:42.190 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第821/1000条: (113.669982,24.608317) -> (113.610591,24.676035)
2025-08-18 02:07:42.257 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:42.563 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第822/1000条: (113.587335,24.79831) -> (113.560325,24.794646)
2025-08-18 02:07:42.618 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.894089864709295%)
2025-08-18 02:07:42.920 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第823/1000条: (113.681038,24.773209) -> (113.583513,24.792992)
2025-08-18 02:07:42.971 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过15.32%)
2025-08-18 02:07:43.278 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第824/1000条: (113.596481,24.785717) -> (114.211297,24.860204)
2025-08-18 02:07:43.349 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.13%)
2025-08-18 02:07:43.651 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第825/1000条: (114.383163,25.181234) -> (113.038563,25.287388)
2025-08-18 02:07:43.765 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:07:44.068 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第826/1000条: (113.595139,24.711635) -> (112.993537,25.164346)
2025-08-18 02:07:44.143 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过85.11%)
2025-08-18 02:07:44.456 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第827/1000条: (113.649697,24.811537) -> (113.556852,24.849902)
2025-08-18 02:07:44.507 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:44.816 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第828/1000条: (114.295215,25.12837) -> (113.774273,24.916178)
2025-08-18 02:07:44.891 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过47.69%)
2025-08-18 02:07:45.192 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第829/1000条: (114.617453,25.193391) -> (113.563664,24.797739)
2025-08-18 02:07:45.297 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.26%)
2025-08-18 02:07:45.613 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第830/1000条: (113.609192,24.683351) -> (113.460609,24.979835)
2025-08-18 02:07:45.697 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:46.003 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第831/1000条: (114.307626,25.110196) -> (113.394749,25.077987)
2025-08-18 02:07:46.083 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过98.05%)
2025-08-18 02:07:46.388 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第832/1000条: (113.285986,24.77544) -> (113.586887,24.807675)
2025-08-18 02:07:46.473 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过29.35%)
2025-08-18 02:07:46.779 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第833/1000条: (114.308262,25.115691) -> (114.310526,25.114721)
2025-08-18 02:07:46.831 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.868263267454701%)
2025-08-18 02:07:47.137 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第834/1000条: (113.580148,24.798294) -> (113.021688,25.304575)
2025-08-18 02:07:47.226 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.97%)
2025-08-18 02:07:47.540 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第835/1000条: (113.740337,25.086177) -> (113.594271,24.68996)
2025-08-18 02:07:47.602 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:47.917 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第836/1000条: (113.582654,24.782583) -> (114.114166,24.921534)
2025-08-18 02:07:48.002 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.81%)
2025-08-18 02:07:48.306 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第837/1000条: (114.12744,24.359304) -> (114.131501,24.348938)
2025-08-18 02:07:48.353 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:48.665 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第838/1000条: (113.532905,24.545201) -> (113.280856,24.777363)
2025-08-18 02:07:48.742 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过43.38%)
2025-08-18 02:07:49.053 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第839/1000条: (113.828043,24.372661) -> (113.813891,24.365936)
2025-08-18 02:07:49.104 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过5.590949462327498%)
2025-08-18 02:07:49.410 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第840/1000条: (113.713459,24.849254) -> (113.582902,24.736381)
2025-08-18 02:07:49.477 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:49.784 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第841/1000条: (114.278287,25.097139) -> (114.692249,25.281171)
2025-08-18 02:07:49.861 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.1%)
2025-08-18 02:07:50.176 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第842/1000条: (114.064974,24.950458) -> (113.347801,25.132972)
2025-08-18 02:07:50.270 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过76.66%)
2025-08-18 02:07:50.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第843/1000条: (113.310888,24.75691) -> (113.356817,25.123887)
2025-08-18 02:07:50.665 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:50.967 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第844/1000条: (114.075691,24.964826) -> (113.230865,24.622416)
2025-08-18 02:07:51.049 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.19%)
2025-08-18 02:07:51.359 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第845/1000条: (113.809305,24.786451) -> (114.597295,25.259684)
2025-08-18 02:07:51.455 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过103.57%)
2025-08-18 02:07:51.763 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第846/1000条: (113.614493,24.675472) -> (114.197967,24.046949)
2025-08-18 02:07:51.865 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过106.49%)
2025-08-18 02:07:52.167 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第847/1000条: (113.5331,24.788302) -> (113.522588,24.775234)
2025-08-18 02:07:52.226 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.01%)
2025-08-18 02:07:52.537 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第848/1000条: (113.286887,24.776535) -> (114.213624,24.86068)
2025-08-18 02:07:52.618 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.88%)
2025-08-18 02:07:52.927 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第849/1000条: (113.421515,25.106846) -> (113.566906,24.538753)
2025-08-18 02:07:53.024 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过86.08%)
2025-08-18 02:07:53.329 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第850/1000条: (113.344814,25.126402) -> (113.528143,24.78718)
2025-08-18 02:07:53.407 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过50.16%)
2025-08-18 02:07:53.721 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第851/1000条: (114.151219,25.024005) -> (113.467777,25.153966)
2025-08-18 02:07:53.828 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.59%)
2025-08-18 02:07:54.143 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第852/1000条: (113.627145,24.791868) -> (113.563022,24.96761)
2025-08-18 02:07:54.201 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.58%)
2025-08-18 02:07:54.515 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第853/1000条: (114.453055,25.261355) -> (113.830642,25.056242)
2025-08-18 02:07:54.606 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.05%)
2025-08-18 02:07:54.919 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第854/1000条: (114.421616,25.14868) -> (114.307452,25.115455)
2025-08-18 02:07:54.978 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.54%)
2025-08-18 02:07:55.293 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第855/1000条: (113.856931,25.222093) -> (113.58063,24.747596)
2025-08-18 02:07:55.365 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:55.679 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第856/1000条: (114.289174,25.106499) -> (113.306177,24.744979)
2025-08-18 02:07:55.770 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过95.55%)
2025-08-18 02:07:56.087 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第857/1000条: (113.577958,24.795753) -> (113.592617,24.80643)
2025-08-18 02:07:56.142 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.129031152563552%)
2025-08-18 02:07:56.445 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第858/1000条: (114.067355,24.946552) -> (114.289174,25.106499)
2025-08-18 02:07:56.498 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.91751262861188%)
2025-08-18 02:07:56.800 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第859/1000条: (113.580549,24.739854) -> (113.605244,24.809224)
2025-08-18 02:07:56.852 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过14.331737226985087%)
2025-08-18 02:07:57.161 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第860/1000条: (113.596707,24.824025) -> (113.892354,25.138036)
2025-08-18 02:07:57.246 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过66.42%)
2025-08-18 02:07:57.550 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第861/1000条: (113.619987,24.658639) -> (113.634005,24.817828)
2025-08-18 02:07:57.603 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.33%)
2025-08-18 02:07:57.909 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第862/1000条: (113.586858,24.817181) -> (113.588071,24.828112)
2025-08-18 02:07:57.964 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:58.269 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第863/1000条: (113.280856,24.777363) -> (113.571075,24.798753)
2025-08-18 02:07:58.343 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:07:58.643 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第864/1000条: (113.60408,24.673761) -> (113.358375,25.159845)
2025-08-18 02:07:58.740 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过63.79%)
2025-08-18 02:07:59.048 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第865/1000条: (114.467663,25.147045) -> (114.456557,25.258442)
2025-08-18 02:07:59.114 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.627538666870997%)
2025-08-18 02:07:59.424 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第866/1000条: (113.587449,24.794473) -> (113.583417,24.797819)
2025-08-18 02:07:59.477 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过2.73%)
2025-08-18 02:07:59.779 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第867/1000条: (113.626033,24.692593) -> (113.621002,24.506921)
2025-08-18 02:07:59.844 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.27419388011305%)
2025-08-18 02:08:00.150 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第868/1000条: (114.620584,25.214766) -> (113.599095,24.785707)
2025-08-18 02:08:00.253 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过103.49%)
2025-08-18 02:08:00.554 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第869/1000条: (113.584003,24.780365) -> (114.32763,24.9223)
2025-08-18 02:08:00.646 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.72%)
2025-08-18 02:08:00.957 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第870/1000条: (114.073295,24.936408) -> (113.594489,24.685941)
2025-08-18 02:08:01.025 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过60.93%)
2025-08-18 02:08:01.333 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第871/1000条: (113.528143,24.78718) -> (114.377967,25.158692)
2025-08-18 02:08:01.407 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过80.07%)
2025-08-18 02:08:01.722 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第872/1000条: (113.581993,24.742679) -> (113.523381,24.779982)
2025-08-18 02:08:01.789 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过11.47%)
2025-08-18 02:08:02.094 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第873/1000条: (114.075691,24.964826) -> (113.356024,25.124669)
2025-08-18 02:08:02.196 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.79%)
2025-08-18 02:08:02.502 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第874/1000条: (113.147422,24.987141) -> (113.359923,25.119563)
2025-08-18 02:08:02.565 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过42.148917309298724%)
2025-08-18 02:08:02.872 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第875/1000条: (113.352679,25.130516) -> (113.394749,25.077987)
2025-08-18 02:08:02.944 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过8.26%)
2025-08-18 02:08:03.248 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第876/1000条: (113.040821,25.286564) -> (114.216857,24.056242)
2025-08-18 02:08:03.361 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:03.672 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第877/1000条: (113.595394,24.807711) -> (113.592617,24.80643)
2025-08-18 02:08:03.727 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.38%)
2025-08-18 02:08:04.031 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第878/1000条: (114.291887,25.10602) -> (114.21776,24.061352)
2025-08-18 02:08:04.142 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过113.07%)
2025-08-18 02:08:04.453 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第879/1000条: (113.11442,25.238855) -> (114.140936,24.354225)
2025-08-18 02:08:04.571 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:04.875 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第880/1000条: (114.317904,25.121835) -> (113.582594,24.78169)
2025-08-18 02:08:04.951 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.27%)
2025-08-18 02:08:05.266 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第881/1000条: (113.342662,25.124983) -> (113.14235,24.917176)
2025-08-18 02:08:05.345 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.27087604782296%)
2025-08-18 02:08:05.657 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第882/1000条: (113.357829,25.122181) -> (113.584454,24.782079)
2025-08-18 02:08:05.720 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过51.15%)
2025-08-18 02:08:06.029 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第883/1000条: (113.846576,24.807736) -> (113.605636,24.675586)
2025-08-18 02:08:06.091 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:06.404 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第884/1000条: (113.578044,24.794004) -> (114.049258,24.954539)
2025-08-18 02:08:06.484 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.73%)
2025-08-18 02:08:06.793 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第885/1000条: (113.605291,24.841961) -> (113.581383,24.681744)
2025-08-18 02:08:06.850 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.96%)
2025-08-18 02:08:07.154 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第886/1000条: (114.075691,24.964826) -> (114.128987,24.349707)
2025-08-18 02:08:07.244 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.19%)
2025-08-18 02:08:07.560 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第887/1000条: (113.788491,24.783949) -> (113.577083,24.797087)
2025-08-18 02:08:07.644 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.97%)
2025-08-18 02:08:07.948 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第888/1000条: (113.579968,24.797236) -> (113.597511,24.672527)
2025-08-18 02:08:08.014 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.55%)
2025-08-18 02:08:08.322 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第889/1000条: (114.497229,25.236103) -> (114.313104,25.121228)
2025-08-18 02:08:08.389 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.58%)
2025-08-18 02:08:08.696 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第890/1000条: (113.461961,24.728928) -> (113.733547,25.257812)
2025-08-18 02:08:08.766 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过69.52%)
2025-08-18 02:08:09.072 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第891/1000条: (113.521811,24.75693) -> (113.11442,25.238855)
2025-08-18 02:08:09.172 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过57.21%)
2025-08-18 02:08:09.476 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第892/1000条: (113.579177,24.793081) -> (113.587371,24.818606)
2025-08-18 02:08:09.530 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.649160530042668%)
2025-08-18 02:08:09.831 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第893/1000条: (114.152008,25.024583) -> (113.362158,25.212418)
2025-08-18 02:08:09.937 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过90.73%)
2025-08-18 02:08:10.241 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第894/1000条: (114.312124,25.122052) -> (113.993068,24.62976)
2025-08-18 02:08:10.329 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:10.646 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第895/1000条: (114.590166,25.198902) -> (114.405841,25.172917)
2025-08-18 02:08:10.706 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.25%)
2025-08-18 02:08:11.019 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第896/1000条: (113.545597,24.771042) -> (113.533624,24.891498)
2025-08-18 02:08:11.086 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.75%)
2025-08-18 02:08:11.391 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第897/1000条: (114.529399,25.28035) -> (113.58508,24.798956)
2025-08-18 02:08:11.492 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过97.22%)
2025-08-18 02:08:11.795 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第898/1000条: (114.32763,24.9223) -> (113.061544,25.282525)
2025-08-18 02:08:11.883 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:12.184 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第899/1000条: (113.568462,24.799766) -> (113.526181,24.786133)
2025-08-18 02:08:12.240 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:12.544 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第900/1000条: (113.273147,24.775665) -> (113.298075,24.776911)
2025-08-18 02:08:12.598 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.903160915735562%)
2025-08-18 02:08:12.902 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第901/1000条: (113.528143,24.78718) -> (113.588102,24.809177)
2025-08-18 02:08:12.970 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:13.276 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第902/1000条: (113.089315,24.921496) -> (113.285986,24.77544)
2025-08-18 02:08:13.338 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.28%)
2025-08-18 02:08:13.648 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第903/1000条: (114.383163,25.181234) -> (114.309243,25.119572)
2025-08-18 02:08:13.713 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.32%)
2025-08-18 02:08:14.024 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第904/1000条: (113.710448,24.746525) -> (113.937188,25.303314)
2025-08-18 02:08:14.093 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:14.396 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第905/1000条: (113.043141,25.084925) -> (113.04757,25.28601)
2025-08-18 02:08:14.450 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:14.755 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第906/1000条: (114.289258,25.098861) -> (113.329595,25.110851)
2025-08-18 02:08:14.835 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过102.49%)
2025-08-18 02:08:15.144 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第907/1000条: (113.530886,24.847659) -> (114.308292,25.119033)
2025-08-18 02:08:15.233 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过75.13%)
2025-08-18 02:08:15.548 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第908/1000条: (114.399033,25.180366) -> (114.082915,24.944561)
2025-08-18 02:08:15.621 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过38.21%)
2025-08-18 02:08:15.935 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第909/1000条: (113.541131,24.800106) -> (113.587371,24.818606)
2025-08-18 02:08:16.003 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过9.374004305595436%)
2025-08-18 02:08:16.307 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第910/1000条: (113.58453,24.765113) -> (114.325144,24.121829)
2025-08-18 02:08:16.425 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过115.48%)
2025-08-18 02:08:16.729 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第911/1000条: (114.308262,25.115691) -> (113.58508,24.798956)
2025-08-18 02:08:16.877 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过70.53%)
2025-08-18 02:08:17.192 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第912/1000条: (114.145375,24.919113) -> (113.551773,24.77057)
2025-08-18 02:08:17.257 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:17.565 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第913/1000条: (113.287117,24.777588) -> (113.548193,24.769055)
2025-08-18 02:08:17.638 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过26.19%)
2025-08-18 02:08:17.950 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第914/1000条: (114.02538,25.185996) -> (113.26311,24.773254)
2025-08-18 02:08:18.037 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:18.341 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第915/1000条: (114.294109,25.112283) -> (113.592008,24.793988)
2025-08-18 02:08:18.427 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过67.39%)
2025-08-18 02:08:18.730 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第916/1000条: (113.345067,24.517248) -> (114.126064,24.414283)
2025-08-18 02:08:18.830 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过109.86%)
2025-08-18 02:08:19.136 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第917/1000条: (113.598486,24.787637) -> (113.573594,24.803286)
2025-08-18 02:08:19.198 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.42%)
2025-08-18 02:08:19.512 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第918/1000条: (113.60309,24.809009) -> (113.609488,24.815602)
2025-08-18 02:08:19.563 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过1.92%)
2025-08-18 02:08:19.871 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第919/1000条: (113.358771,25.109612) -> (113.427912,24.944279)
2025-08-18 02:08:19.925 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过22.32%)
2025-08-18 02:08:20.227 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第920/1000条: (113.595021,24.669227) -> (114.11656,24.354095)
2025-08-18 02:08:20.319 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过68.8%)
2025-08-18 02:08:20.632 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第921/1000条: (114.456557,25.258442) -> (114.061965,24.948373)
2025-08-18 02:08:20.693 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.83554175071156%)
2025-08-18 02:08:21.002 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第922/1000条: (113.669818,24.607856) -> (113.603412,24.575727)
2025-08-18 02:08:21.064 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.03150562593858%)
2025-08-18 02:08:21.375 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第923/1000条: (113.294488,24.780894) -> (113.603412,24.575727)
2025-08-18 02:08:21.451 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过33.51%)
2025-08-18 02:08:21.766 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第924/1000条: (113.579595,24.794555) -> (113.288158,25.356661)
2025-08-18 02:08:21.856 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过91.44%)
2025-08-18 02:08:22.156 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第925/1000条: (113.355101,25.116801) -> (114.071887,24.945179)
2025-08-18 02:08:22.263 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过84.18%)
2025-08-18 02:08:22.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第926/1000条: (113.591159,24.67104) -> (113.621164,24.688329)
2025-08-18 02:08:22.622 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过7.706848213905038%)
2025-08-18 02:08:22.936 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第927/1000条: (113.982329,24.608784) -> (114.071887,24.945179)
2025-08-18 02:08:23.015 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.67763328555905%)
2025-08-18 02:08:23.326 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第928/1000条: (113.433509,24.72345) -> (113.432648,24.948648)
2025-08-18 02:08:23.384 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:23.685 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第929/1000条: (113.631825,24.817368) -> (113.58453,24.765113)
2025-08-18 02:08:23.750 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:24.059 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第930/1000条: (114.310006,25.112769) -> (113.57365,24.726094)
2025-08-18 02:08:24.126 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.13%)
2025-08-18 02:08:24.433 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第931/1000条: (113.272826,24.772738) -> (113.524565,24.787409)
2025-08-18 02:08:24.484 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过24.28%)
2025-08-18 02:08:24.794 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第932/1000条: (113.615234,24.795495) -> (113.592686,24.6663)
2025-08-18 02:08:24.851 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过20.96%)
2025-08-18 02:08:25.167 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第933/1000条: (113.391712,24.829461) -> (113.071999,25.180093)
2025-08-18 02:08:25.252 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过58.38%)
2025-08-18 02:08:25.562 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第934/1000条: (113.394954,25.078365) -> (113.599248,24.799582)
2025-08-18 02:08:25.623 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过41.56%)
2025-08-18 02:08:25.937 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第935/1000条: (113.053227,25.28534) -> (113.356061,25.198235)
2025-08-18 02:08:26.021 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过48.26813969862652%)
2025-08-18 02:08:26.330 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第936/1000条: (114.139647,24.918033) -> (113.584454,24.782079)
2025-08-18 02:08:26.418 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.09%)
2025-08-18 02:08:26.724 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第937/1000条: (114.026744,25.188164) -> (113.499321,24.678969)
2025-08-18 02:08:26.823 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过116.51792813218087%)
2025-08-18 02:08:27.133 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第938/1000条: (113.595397,24.679217) -> (113.15305,25.378223)
2025-08-18 02:08:27.236 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过94.68%)
2025-08-18 02:08:27.542 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第939/1000条: (113.613272,24.82832) -> (113.40336,25.071192)
2025-08-18 02:08:27.618 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:27.926 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第940/1000条: (114.650769,25.13671) -> (113.545994,24.738085)
2025-08-18 02:08:28.033 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过119.6%)
2025-08-18 02:08:28.346 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第941/1000条: (114.405841,25.172917) -> (114.134313,24.354659)
2025-08-18 02:08:28.422 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过88.21%)
2025-08-18 02:08:28.725 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第942/1000条: (113.578678,24.723723) -> (114.135265,24.356764)
2025-08-18 02:08:28.811 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过74.88%)
2025-08-18 02:08:29.117 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第943/1000条: (113.275042,24.776658) -> (113.581662,24.79559)
2025-08-18 02:08:29.198 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过30.99%)
2025-08-18 02:08:29.507 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第944/1000条: (113.743802,25.083982) -> (113.345161,24.515271)
2025-08-18 02:08:29.577 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:29.884 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第945/1000条: (113.355101,25.116801) -> (113.17706,25.439784)
2025-08-18 02:08:29.971 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过61.36%)
2025-08-18 02:08:30.273 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第946/1000条: (113.351508,25.128528) -> (113.357963,25.125335)
2025-08-18 02:08:30.331 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.0188155025075396%)
2025-08-18 02:08:30.634 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第947/1000条: (113.564343,24.804194) -> (113.59015,24.814693)
2025-08-18 02:08:30.683 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.640124790296898%)
2025-08-18 02:08:30.995 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第948/1000条: (113.765271,25.101996) -> (113.568125,24.707039)
2025-08-18 02:08:31.075 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:31.379 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第949/1000条: (113.5078,24.760944) -> (113.58238,24.775981)
2025-08-18 02:08:31.462 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过13.080221452278286%)
2025-08-18 02:08:31.772 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第950/1000条: (114.348077,25.315945) -> (113.572042,24.822559)
2025-08-18 02:08:31.861 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过81.02%)
2025-08-18 02:08:32.167 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第951/1000条: (113.405657,24.870915) -> (113.368073,24.836505)
2025-08-18 02:08:32.211 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过12.149402360028208%)
2025-08-18 02:08:32.512 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第952/1000条: (113.670631,24.61117) -> (113.588587,24.818755)
2025-08-18 02:08:32.577 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过27.47%)
2025-08-18 02:08:32.890 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第953/1000条: (113.621871,24.821854) -> (113.610525,24.810531)
2025-08-18 02:08:32.934 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.07%)
2025-08-18 02:08:33.237 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第954/1000条: (113.604289,24.805689) -> (113.59796,24.675879)
2025-08-18 02:08:33.316 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过17.12%)
2025-08-18 02:08:33.628 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第955/1000条: (113.458656,24.74558) -> (113.552856,24.800859)
2025-08-18 02:08:33.695 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:34.006 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第956/1000条: (113.41161,25.13777) -> (114.160942,24.382143)
2025-08-18 02:08:34.135 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:34.446 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第957/1000条: (113.427846,24.951118) -> (113.748088,25.090971)
2025-08-18 02:08:34.525 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%
2025-08-18 02:08:34.837 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第958/1000条: (113.220719,25.399777) -> (114.076363,24.950534)
2025-08-18 02:08:34.956 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:35.259 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第959/1000条: (113.0448,25.08635) -> (113.636468,24.664952)
2025-08-18 02:08:35.358 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过91.18%)
2025-08-18 02:08:35.664 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第960/1000条: (113.599691,24.799928) -> (113.575352,24.806232)
2025-08-18 02:08:35.720 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过6.09%)
2025-08-18 02:08:36.024 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第961/1000条: (113.819598,24.790325) -> (113.600182,24.67531)
2025-08-18 02:08:36.106 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过31.35%)
2025-08-18 02:08:36.419 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第962/1000条: (113.457207,24.728261) -> (113.575736,24.798143)
2025-08-18 02:08:36.485 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过23.901562405409102%)
2025-08-18 02:08:36.796 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第963/1000条: (113.583576,24.751936) -> (114.214861,24.059694)
2025-08-18 02:08:36.905 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过117.26%)
2025-08-18 02:08:37.208 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第964/1000条: (113.592613,24.770538) -> (112.993537,25.164346)
2025-08-18 02:08:37.289 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过87.53%)
2025-08-18 02:08:37.602 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第965/1000条: (114.065794,24.942697) -> (114.40068,25.170415)
2025-08-18 02:08:37.678 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过63.40680109165068%)
2025-08-18 02:08:37.980 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第966/1000条: (113.596128,24.811885) -> (113.269044,24.778633)
2025-08-18 02:08:38.056 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.33%)
2025-08-18 02:08:38.358 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第967/1000条: (114.121644,24.899772) -> (113.588595,24.660835)
2025-08-18 02:08:38.455 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过64.52%)
2025-08-18 02:08:38.766 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第968/1000条: (113.595824,24.804988) -> (114.131782,24.347919)
2025-08-18 02:08:38.846 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过83.52%)
2025-08-18 02:08:39.153 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第969/1000条: (113.410968,24.894023) -> (113.589291,24.767901)
2025-08-18 02:08:39.236 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过32.22%)
2025-08-18 02:08:39.543 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第970/1000条: (113.517586,24.751701) -> (113.52998,24.767209)
2025-08-18 02:08:39.595 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过3.99%)
2025-08-18 02:08:39.906 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第971/1000条: (114.317904,25.121835) -> (114.164194,24.552682)
2025-08-18 02:08:39.997 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过71.55%)
2025-08-18 02:08:40.300 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第972/1000条: (113.278651,24.769408) -> (113.588044,24.804015)
2025-08-18 02:08:40.375 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.94%)
2025-08-18 02:08:40.676 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第973/1000条: (113.982329,24.608784) -> (114.211297,24.860204)
2025-08-18 02:08:40.754 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过59.34008153442312%)
2025-08-18 02:08:41.070 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第974/1000条: (113.610525,24.810531) -> (112.956659,25.334244)
2025-08-18 02:08:41.393 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过93.33%)
2025-08-18 02:08:41.703 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第975/1000条: (113.576249,24.80108) -> (113.588855,24.828047)
2025-08-18 02:08:41.752 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过4.86%)
2025-08-18 02:08:42.064 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第976/1000条: (113.11442,25.238855) -> (113.592686,24.6663)
2025-08-18 02:08:42.160 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过72.23%)
2025-08-18 02:08:42.474 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第977/1000条: (113.16072,25.464952) -> (113.467777,25.153966)
2025-08-18 02:08:42.552 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过73.62%)
2025-08-18 02:08:42.864 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第978/1000条: (114.318552,25.112633) -> (113.053227,25.28534)
2025-08-18 02:08:42.964 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:43.273 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第979/1000条: (113.52577,24.785277) -> (113.828252,24.373631)
2025-08-18 02:08:43.346 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过54.45%)
2025-08-18 02:08:43.650 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第980/1000条: (113.341392,25.125613) -> (113.595545,24.813051)
2025-08-18 02:08:43.711 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过49.03%)
2025-08-18 02:08:44.015 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第981/1000条: (113.059706,25.279947) -> (113.881513,24.273291)
2025-08-18 02:08:44.107 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过120.0%)
2025-08-18 02:08:44.407 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第982/1000条: (113.598416,24.80681) -> (113.757643,25.095525)
2025-08-18 02:08:44.483 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过38.54%)
2025-08-18 02:08:44.800 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第983/1000条: (113.410968,24.894023) -> (113.576893,24.819747)
2025-08-18 02:08:44.875 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过28.18%)
2025-08-18 02:08:45.177 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第984/1000条: (113.523381,24.779982) -> (114.120627,24.938005)
2025-08-18 02:08:45.249 [main] WARN  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - ⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过56.29%)
2025-08-18 02:08:45.554 [main] INFO  c.i.y.pathcalculate.SimpleTravelTimeValidationTest - 🔍 验证第985/1000条: (113.285455,24.76836) -> (114.199906,24.051526)
